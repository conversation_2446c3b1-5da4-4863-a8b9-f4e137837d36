class AiTutorRagConfig < ApplicationRecord
  belongs_to :ai_tutor_agent
  belongs_to :school

  # Configure encryption for sensitive data
  encrypts :pinecone_api_key

  # Validation
  validates :ai_tutor_agent_id, presence: true, uniqueness: true
  validates :default_top_k, presence: true, numericality: {
    greater_than: 0,
    less_than_or_equal_to: 50
  }
  validates :default_namespace, allow_blank: true, length: { maximum: 100 }
  validates :similarity_threshold, presence: true, numericality: {
    greater_than_or_equal_to: 0.0,
    less_than_or_equal_to: 1.0
  }
  validates :enabled, inclusion: { in: [true, false] }
  validates :pinecone_api_key, allow_blank: true, length: { maximum: 500 }

  # Default values
  after_initialize :set_defaults, if: :new_record?

  # Scopes
  scope :enabled, -> { where(enabled: true) }
  scope :by_agent, ->(agent) { where(ai_tutor_agent: agent) }
  scope :by_school, ->(school) { where(school: school) }

  # Class methods
  def self.for_agent(agent)
    find_by(ai_tutor_agent: agent)
  end

  def self.enabled_for_agent(agent)
    config = for_agent(agent)
    config&.enabled? ? config : nil
  end

  def self.for_school(school)
    school.ai_tutor_rag_configs.first
  end

  def self.enabled_for_school(school)
    config = for_school(school)
    config&.enabled? ? config : nil
  end

  def search_params_for(custom_params = {})
    {
      top_k: default_top_k,
      namespace: default_namespace,
      similarity_threshold: similarity_threshold,
      include_metadata: true,
      filter: build_filter(custom_params)
    }
  end

  def build_filter(custom_filter = {})
    custom_filter = (custom_filter || {}).with_indifferent_access
    if custom_filter[:lesson_id].present?
      # for lesson rag
      {
        'source' => custom_filter[:lesson_id].to_s
      }
    else
      # for agent rag
      {
        'ai_tutor_agent_id' => ai_tutor_agent_id.to_s
      }
    end
  end

  def available_namespaces
    if ENV['PINECONE_NAMESPACE'].present?
      [
        { value: ENV['PINECONE_NAMESPACE'], label: ENV['PINECONE_NAMESPACE'] }
      ]
    else
      [
        { value: 'development', label: 'development' }
      ]
    end
  end

  def update_usage_stats!
    increment!(:total_searches)
    update!(last_used_at: Time.current)
  end

  def reset_stats!
    update!(
      total_searches: 0,
      successful_searches: 0,
      failed_searches: 0,
      last_used_at: nil
    )
  end

  def success_rate
    return 0 if total_searches == 0

    (successful_searches.to_f / total_searches * 100).round(2)
  end

  def increment_successful_search!
    increment!(:successful_searches)
    update_usage_stats!
  end

  def increment_failed_search!
    increment!(:failed_searches)
    update_usage_stats!
  end

  def to_search_config
    {
      enabled: enabled?,
      default_top_k: default_top_k,
      default_namespace: default_namespace,
      similarity_threshold: similarity_threshold,
      include_metadata: true,
      max_results: max_results,
      school_id: school_id,
      custom_namespaces: custom_namespaces,
      pinecone_api_key: pinecone_api_key
    }
  end

  def effective_pinecone_api_key
    pinecone_api_key.presence || ENV['PINECONE_API_KEY']
  end

  private

  def set_defaults
    self.enabled = true if enabled.nil?
    self.default_top_k ||= 5
    self.similarity_threshold ||= 0.7
    self.include_metadata = true if include_metadata.nil?
    self.max_results ||= 20
    self.total_searches ||= 0
    self.successful_searches ||= 0
    self.failed_searches ||= 0
  end
end
