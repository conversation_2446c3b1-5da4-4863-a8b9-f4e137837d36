@import "simplemde.min";

.height-700 {
  min-height: 700px;
}
body {
  font-size: 14px !important;
  line-height: 1.6;
  background: #efefef !important;
  font-family: "Hiragino Sans", "ヒラギノ角ゴ ProN W3",
    "Hiragino Kaku Gothic ProN", <PERSON><PERSON>, "メイリオ", Osaka, "ＭＳ Ｐゴシック",
    "MS P Gothic", sans-serif;
  font-weight: 300;
}

//HOME
h1.catch {
  font-size: 4.0rem;
}

#public {
  background: white;
  .navbar-inverse {
    background-color: white;
    border-color: #ccc;
  }
}

li {
  list-style-type: none;
}
ul,
ol {
  padding: 0;
}
.navbar-brand {
  margin-top: -7px;
  img {
    height: 30px;
  }
}

.entry-date {
  border: 1px solid #eee;
  text-align: center;
  font-weight: bold;
  background: white;
  width: 65%;
  width: fit-content;
  white-space: nowrap;
}
.side-milestones {
  .entry-date {
    width: 100%;
  }
}
.entry-date .date {
  color: #333;
  font-size: 17px;
  padding: 5px;
}
.next-week {
  color: white;
  background: darkred;
  font-weight: normal;
  font-size: 90%;
}
.gray {
  color: #999;
}
.normal {
  font-weight: normal;
}
.milestone {
  h2 {
    margin-top: 0;
  }
}
.content {
  margin-top: 60px;
}
.contain {
  padding: 80px 0;
  background: image-url("bg_gradient.png");
}
.margin-bottom-10,
.mb-10 {
  margin-bottom: 10px;
}
.margin-bottom-20,
.mb-20 {
  margin-bottom: 20px;
}
.margin-top-10,
.mt-10 {
  margin-top: 10px;
}
.margin-top-20,
.mt-20 {
  margin-top: 20px;
}
.margin-left-10,
.ms-10 {
  margin-left: 10px;
}
.margin-left-20,
.ms-20 {
  margin-left: 20px;
}
.margin-right-10,
.me-10 {
  margin-right: 10px;
}
.margin-right-20,
.me-20 {
  margin-right: 20px;
}
.mt-1 {
  margin-top: 3rem;
}
.p-1 {
  padding: 3rem;
}
.padding-20 {
  padding: 20px;
}
.align-right {
  text-align: right;
}
.align-center {
  text-align: center;
}
.float-right {
  float: right;
}
.float-left {
  float: left;
}
.underline {
  border-bottom: 1px #eee solid;
}
.big-font {
  font-size: 700%;
}
.big-font2,
.big-font3 {
  font-size: 28px;
  font-weight: bold;
}
.big-font6 {
  font-size: 150%;
}
.padding-0 {
  padding: 0;
}
.underline {
  border-bottom: 1px solid #eee;
  padding: 5px 0;
}
.underline-link {
  text-decoration: underline;
}
.padding-top-10 {
  padding-top: 10px;
}
.padding-top-20 {
  padding-top: 20px;
}
.padding-bottom-20 {
  padding-bottom: 20px;
}
.backimage {
  height: 0;
  padding-top: 56.25%;
  background: url("premium.jpg");
  background-size: contain;
}
.info {
  padding: 20px;
  background: #ccedff;
}
.num-square {
  padding: 2px 10px;
  border: 3px solid #eee;
  margin-right: 10px;
  background: #ccc;
  color: white;
  font-size: 20px;
  font-weight: bold;
}
.white {
  color: white;
}
.clearboth {
  clear: both;
}
td.image20 {
  width: 20%;
}
.thumb-padding {
  padding: 0 0 0 15px;
}
.inline-form {
  display: inline;
}
.form-group.courses {
  img {
    margin: 0 10px;
  }
}
.nowrap {
  white-space: nowrap;
}
.table-fixed {
  table-layout: fixed;
  width: 100%;
}
.margin-bottom-20 {
  margin-bottom: 20px !important;
}
.right-bottom {
}
.img.border {
  border: 1px #eee solid;
}
.more {
  a {
    font-size: 150%;
    text-decoration: underline;
  }
}

.btn-freelance {
  color: #fff;
  background-color: #59d002;
  border-color: #59d002;
}
.btn-freelance:hover {
  color: #fff;
  background-color: #83cc4e;
  border-color: #59d002;
}

//=======public header

.dropdown-toggle:active,
.open .dropdown-toggle {
  background: none !important;
  color: #000 !important;
}
//=======Notification
.bgr-nav {
  background-color: rgb(234, 237, 226);
  // height: 50px;
}
.navbar {
  .container {
    padding: 0px;
    .row {
      width: 100%;
      margin: 0;
    }
  }
  .navbar-container {
    padding: 0px;
    align-items: center;
    display: flex;
  }
  .fa-globe-asia {
    color: #5353c2;
    font-size: 20px;
  }
}
.badge-notify {
  background: red;
  position: absolute;
  top: -7px;
  left: 10px;
  font-weight: normal;
  font-size: 75%;
}

//======Modal
.modal-dialog {
  //  width: 730px;
  margin: 30px auto;
  img,
  video,
  audio {
    max-width: 100% !important;
  }
}

.notification-icon {
  width: 20px;
  overflow: hidden;
}

.notification-icon.empty {
  color: #ccc !important;
}
.side-user-image-box {
  height: 65px;
  overflow: hidden;
  margin-bottom: 10px;
  display: flex;
  justify-content: center;
  align-items: center;
}

//=====Sidebar
.sidebar-nav .sidebar-brand,
.sidebar-nav1 .sidebar-brand,
.sidebar-nav2 .sidebar-brand {
  height: inherit;
}
.sidebar {
  .nav {
    li {
      background: #eee;
    }
    li a {
      background: white;
      padding: 5px 0;
    }
  }
}
#sidebar-wrapper {
  a {
    display: inline;
  }
}

//======= Footer
.footer {
  overflow: hidden;
  width: 100%;

  background-color: #f5f5f5;
}
.footer > .container {
  padding-right: 15px;
  padding-left: 15px;
}

//======= Top page
.top-bigimage {
  max-height: 500px;
  padding: 20px 10%;
  overflow: hidden;
  padding-left: 0px;
  padding-right: 0px;

  img {
    width: 100%;
  }
}
.main-catch {
  width: 70%;
  color: white;
  margin: 0 auto;
}
.jumbotron {
  background: transparent !important;
}
.feature-image {
  text-align: center;
  padding: 20px 0;
  img {
    width: 100%;
    max-height: 200px;
    object-fit: scale-down;
  }
}

.goal-image {
  background: #ccc;
}

//======= Application html =====
.navbar-nav li {
  padding: 15px;
  padding-left: 10px;
}
.navbar-nav li {
  a {
    padding: 0;
  }
  a:hover {
    color: gray !important;
  }
}

//======= Front page=========
.card-height {
  min-height: 250px;
}

//========== Goal layout ==========

.box-shadow {
  -webkit-box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  box-shadow: 0 0 10px 0 rgba(0, 0, 0, 0.1);
  width: 100%;
  margin-left: 0;
}
//======== Goal ===========
#goal-info {
  h2 {
    font-size: 18px;
  }
}

//======== Classroom ===========
#classroom {
  background: #e9ebee;
}
.white-back {
  background: white;
}
.padding-20 {
  padding: 20px;
}
.col-md-3.img {
  padding-left: 0;
}
.comment-area {
  background: #eee;
  font-size: 70%;
  padding: 10px 10px 0px 10px;
}
.goal-block {
  li {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 4px;
    font-family: "Montserrat", sans-serif;
    border: 1px dashed #cacaca;
    border-radius: 50px;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 13px;

    .fa {
      color: #ff9c00;
    }
  }
}

//========= font-color ========
.white {
  color: white;
}
//====== Q =========
.q-header {
  background: #e9ebee;
}
.not_answered {
  background: #efefef !important;
}
//=====Course
.course-body {
  font-size: 16px;
}
.pb-60 {
  padding-bottom: 60px;
}
.pt-60 {
  padding-top: 60px;
}

.bgcolor3 {
  background-color: #f7f7f7;
}
.page-header {
  //    background-image: url("https://s3-ap-northeast-1.amazonaws.com/skillhub-images/pops/page-header.jpg");
  background-image: url("bg_material1.jpg");
  background-size: cover;
  text-align: center;
  margin: 0;
  padding: 40px 0;
  h1 {
    color: #fff;
    margin-bottom: 25px;
    font-size: 40px;
  }
  .breadcrumb {
    a {
      color: white;
    }
  }
}
.breadcrumb {
  background-color: transparent;
  font-family: "Montserrat", sans-serif;
  padding: 8px 0px 0;
}
ul.breadcrumb {
  padding: 0;
  margin: 5px 0 0;
}
.fa-money.gold {
  color: white;
}
.course-list.new:before {
  content: "NEW";
  font-family: "Montserrat", sans-serif;
  position: absolute;
  right: 10px;
  width: 40px;
  height: 50px;
  top: 0px;
  color: #fff;
  background-color: #ff9c00;
  padding: 26px 5px 5px 5px;
  font-size: 12px;
  text-align: center;
  z-index: 2;
}
.course-list.new:after {
  content: "";
  position: absolute;
  top: 50px;
  right: 10px;
  width: 0;
  height: 0;
  border-style: solid;
  border-width: 14px 22px 0 18px;
  border-color: #ff9d00 transparent transparent transparent;
  z-index: 2;
}
.course-list {
  border: 1px solid #e5e5e5;
  padding: 4px;
  overflow: hidden;
  position: relative;
  margin: 25px 0px;
  background: white;

  .course-detail {
    margin-left: 2%;
    padding: 15px 10px 10px;
  }

  .course-list .course-media {
    overflow: hidden;
    width: 100%;
    float: left;
  }

  img {
    display: block;
    max-width: 100%;
    height: auto;
  }
  transition: 350ms ease all;
  h1.heading,
  h2.heading,
  h3.heading,
  h4.heading,
  h5.heading,
  h6.heading {
    margin-top: 0em;
    margin-bottom: 1em;
    font-weight: 600;
  }
  .btn-primary {
    background-color: #ff9c00;
    border: none;
  }
  .btn-primary:hover,
  .btn-primary:active,
  .btn-primary:focus,
  .btn-primary:active:focus,
  .btn-primary:active:hover {
    background-color: #ffb035;
  }
  .btn-sm {
    padding: 8px 24px;
  }
  .course-features > li {
    display: inline-block;
    padding: 4px 8px;
    margin: 0 4px;
    font-family: "Montserrat", sans-serif;
    border: 1px dashed #cacaca;
    border-radius: 50px;
    text-transform: uppercase;
    font-weight: 600;
    font-size: 13px;
  }
  .course-features > li .fa {
    color: #ff9c00;
  }
  .fa {
    display: inline-block;
    font: normal normal normal 14px/1 FontAwesome;
    font-size: inherit;
    text-rendering: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
}
.widget-top {
  margin-top: 30px;
}
.widget {
  border: 1px solid #d7d7d7;
  overflow: hidden;
  position: relative;
  padding: 20px;
  margin-bottom: 30px;
  box-shadow: 0px 2px 5px #d7d7d7;
  background-color: #fff;
  .categories {
    list-style: none;
    padding-left: 0;
  }
  .categories > li {
    position: relative;
    padding: 5px 0;
    border-bottom: 1px dotted #d7d7d7;
  }
  .tagcloud {
    margin: 0;
    padding: 0;

    a {
      color: white;
    }
  }
}
.premium-service-back {
  text-align: center;
  background: #eee;
  img {
    display: inline;
    width: 100%;
  }
}
.blog-card {
  border: 1px solid #e5e5e5;
  padding: 4px 4px 15px 4px;
  position: relative;
  overflow: hidden;
  margin: 25px 0px;
  box-shadow: 0px 2px 5px #d7d7d7;
  background-color: #fff;

  .blog-media {
    overflow: hidden;
    height: 200px;
  }

  .blog-meta {
    font-family: "Montserrat", sans-serif;
    color: #cacaca;
    font-size: 13px;
    font-weight: 100;
    text-transform: uppercase;
    padding: 22px 15px 0px;
  }
  .blog-body {
    padding: 0 15px 5px;
    img {
      padding: 5px;
      box-shadow: 0 0 0 1px #ccc;
      margin: 5px auto;
      max-width: 100%;
    }
  }
  .blog-show {
    font-size: 17px;
  }
  h3 {
    font-weight: 600;
    -webkit-transition: 350ms ease all;
    transition: 350ms ease all;
    font-family: "Oswald", sans-serif;
    color: #1a2b51;
    text-transform: uppercase;
    line-height: 1.3em;
  }

  .posted-on {
    position: absolute;
    top: 20px;
    left: 20px;
    font-family: "Montserrat", sans-serif;
    font-weight: 600;
    text-transform: uppercase;
    color: #333;
    border: 2px solid #ff9c00;
    background-color: #fff;
    z-index: 5;

    .date {
      color: #ff9c00;
      display: block;
      text-align: center;
    }
    .month {
      display: block;
      background-color: #1a2b51;
      padding: 4px 8px;
      color: #fff;
    }
  }
}

//********Subscription
.terms-in-use {
  height: 100px;
  overflow: auto;
  border: 1px solid #eee;
  padding: 10px;
}

//******Question
.vote {
  padding-top: 10px;
}
.answer-accepted {
  color: green;
  margin: 20px 0 20px 10px;
  font-size: 30px;
}
.vote-value {
  font-size: 200%;
  font-weight: bold;
  color: #666;
}
.comment-list {
  width: 100%;
  .row {
    margin: 0;
  }
}

//#########Chat
.chat-users {
  display: flex;
  flex-direction: column;
  li {
    background: #eee;
    order: 2;
    &.active {
      order: 1;
    }
  }
  .active {
    background: white;
  }
}
//######### q
.sample {
  background: #eee;
  padding: 10px;
}

//######### shikigaku top
#shikigaku-top {
  font-family: -apple-system, system-ui, BlinkMacSystemFont, "Segoe UI", Roboto,
    "Helvetica Neue", Arial, sans-serif;
  font-size: 1.7rem;
  font-weight: normal;
  line-height: 1.5;
  color: #292b2c;
  background-color: #fff;

  h1 {
    font-size: 265%;
  }

  .link-outline {
    border: #1b2858 2px solid;
    display: block;
    padding: 10px 15px;
    border-radius: 5px;
    font-weight: bold;
  }
  .starter-template {
    padding: 5rem 1.5rem 0 1.5rem;
    text-align: center;
  }
  .mb-5 {
    margin-bottom: 3rem !important;
  }
  .font_mcho {
    font-family: Georgia, 游明朝, "Yu Mincho", YuMincho, "Hiragino Mincho ProN",
      HGS明朝E, メイリオ, Meiryo, serif;
  }
  .text-primary {
    color: #1b2858 !important;
  }
  .lead {
    font-weight: 300;
    strong {
      font-weight: normal;
    }
  }
  p {
    margin-top: 0;
    margin-bottom: 1rem;
  }
  .form-signin {
    max-width: 500px;
    padding: 0 1.5rem;
    margin: 0 auto;
  }
  .btn-primary {
    color: #fff;
    background-color: #1b2858;
    border-color: #1b2858;
  }
  .btn-primary {
    color: #fff;
    background-color: #1b2858;
    border-color: #1b2858;
  }
  .btn-primary:hover {
    color: #fff;
    background-color: #0f1631;
    border-color: #0d1329;
  }
  .footer-top {
    width: 100%;
    padding: 20px 0;
  }
  .mx-auto {
    margin-right: auto !important;
    margin-left: auto !important;
  }
  ul.nav li {
    display: inline-block;
  }
  .gray-back {
    background: #fafbfc;
  }
  a {
    color: #1b2858;
  }
  .p-4 {
    padding: 3rem 1.5rem !important;
  }
  .checkbox {
    margin-bottom: 20px;
  }
  .form-control {
    position: relative;
    height: auto;
    -webkit-box-sizing: border-box;
    box-sizing: border-box;
    padding: 10px;
    font-size: 16px;
  }
  .singin_logo {
    width: 50%;
    max-width: 250px;
    height: auto;
    margin: auto;
  }

  .mb-3 {
    margin-bottom: 1rem !important;
  }
  img {
    vertical-align: middle;
    border-style: none;
  }

  .p-3 {
    padding: 1rem 1rem !important;
  }
}
body.singin {
  width: 100%;
  height: 100%;
  background: image-url("basic-bg.jpeg");
  //      background-image: url(../img/content/singin/singin_bg.jpg);
  background-position: center center;
  background-repeat: no-repeat;
  background-size: cover;
  color: #fff !important;
  background-color: #1c5485 !important;
  a,
  strong {
    color: #fff !important;
  }
  a.nav-link {
    background: inherit;
    &:hover {
      text-decoration: underline;
    }
  }
}

#error_explanation {
  color: rgb(194, 8, 8);
}

#premium_service {
  background: #f0eddf;
  p {
    font-size: 120%;
  }

  h2,
  h3 {
    font-weight: bold;
    color: #555;
    margin-bottom: 20px;
  }

  .feature {
    margin-bottom: 25px;
  }

  .container.section {
    background: white;
    padding: 25px;
    margin-top: 30px;
  }

  .navbar-inverse {
    background-color: #ebede2;
    border-color: #ccc;
  }
}

.question {
  .q-content {
    padding: 0px 0;
  }
  h2 {
    font-size: 150%;
    margin: 10px 0;
  }
}

/* desert scheme ported from vim to google prettify */
pre code {
  box-shadow: none;
  background: #f8f8f8;
}
pre.prettyprint {
  display: block;
  background-color: #333;
}
pre .nocode {
  background-color: none;
  color: #000;
}
pre .str {
  color: #ffa0a0;
} /* string  - pink */
pre .kwd {
  color: #f0e68c;
  font-weight: bold;
}
pre .com {
  color: #87ceeb;
} /* comment - skyblue */
pre .typ {
  color: #98fb98;
} /* type    - lightgreen */
pre .lit {
  color: #cd5c5c;
} /* literal - darkred */
pre .pun {
  color: #fff;
} /* punctuation */
pre .pln {
  color: #fff;
} /* plaintext */
pre .tag {
  color: #f0e68c;
  font-weight: bold;
} /* html/xml tag    - lightyellow */
pre .atn {
  color: #bdb76b;
  font-weight: bold;
} /* attribute name  - khaki */
pre .atv {
  color: #ffa0a0;
} /* attribute value - pink */
pre .dec {
  color: #98fb98;
} /* decimal         - lightgreen */

/* Specify class=linenums on a pre to get line numbering */
ol.linenums {
  margin-top: 0;
  margin-bottom: 0;
  color: #aeaeae;
} /* IE indents via margin-left */
li.L0,
li.L1,
li.L2,
li.L3,
li.L5,
li.L6,
li.L7,
li.L8 {
  list-style-type: none;
}
/* Alternate shading for lines */
li.L1,
li.L3,
li.L5,
li.L7,
li.L9 {
}

@media print {
  pre.prettyprint {
    background-color: none;
  }
  pre .str,
  code .str {
    color: #060;
  }
  pre .kwd,
  code .kwd {
    color: #006;
    font-weight: bold;
  }
  pre .com,
  code .com {
    color: #600;
    font-style: italic;
  }
  pre .typ,
  code .typ {
    color: #404;
    font-weight: bold;
  }
  pre .lit,
  code .lit {
    color: #044;
  }
  pre .pun,
  code .pun {
    color: #440;
  }
  pre .pln,
  code .pln {
    color: #000;
  }
  pre .tag,
  code .tag {
    color: #006;
    font-weight: bold;
  }
  pre .atn,
  code .atn {
    color: #404;
  }
  pre .atv,
  code .atv {
    color: #060;
  }
}
.teacher {
  background: url("teacher.png") no-repeat;
}
.vc {
  //    max-width: calc(100% - 60px);
  width: 100%;
  border: 3px solid #eee;
  background-color: #fff;
  color: #444;
  padding: 2.5%;
  position: relative;
  border-radius: 5px;
}
.irr:after {
  border-right: 10px solid #fff;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  top: 14px;
  left: -7px;
}
.vc:after {
  content: "";
  position: absolute;
}
.irr:before {
  border-right: 8px solid #eee;
  border-bottom: 8px solid transparent;
  border-top: 8px solid transparent;
  top: 14px;
  left: -11px;
}
.vc:before {
  content: "";
  position: absolute;
}
.clearfix:after {
  content: "";
  clear: both;
  display: block;
}
.sn {
  text-align: center;
}

//目次
.table-of-contents {
  ul {
    border: solid 2px #ffb03f;
    padding: 0 0.5em;
    position: relative;
  }

  ul li {
    line-height: 1.5;
    padding: 0.5em 0 0.5em 1.4em;
    border-bottom: dashed 1px silver;
    list-style-type: none !important;
  }

  ul li:before {
    font-family: "Font Awesome 5 Free";
    content: "\f138"; /*アイコン種類*/
    position: absolute;
    left: 0.5em; /*左端からのアイコンまで*/
    color: #ffb03f; /*アイコン色*/
  }

  ul li:last-of-type {
    border-bottom: none;
  }
}

ul.cp_list::after {
  position: absolute;
  content: "目次";
  padding: 1px 7px;
  letter-spacing: 0.05em;
  font-weight: bold;
  font-size: 0.8em;
  background: #ffb03f;
  color: #fff;
  bottom: 100%;
  left: -2px;
  border-radius: 4px 4px 0px 0px;
}

ul.cp_list {
  position: relative;
  padding: 0 0.5em;
  margin-top: 2em;
  list-style-type: none;
  border: solid 2px #ffb03f;
  li {
    line-height: 1.5;
    padding: 0.5em 0 0.5em 1.4em;
    border-bottom: 1px dashed #ffb03f;
  }
  li::before {
    position: absolute;
    content: "\002713";
    color: #ff5722;
    font-weight: bold;
    left: 0.5em;
  }
}
.cke_editable {
  font-size: 15px !important;
}

#select-item.select-item {
  display: none;
}

.select-item-button {
  &:hover {
    background: #ccc;
  }
  min-width: 200px;
}

.popover-content {
  h4 {
    font-size: 16px;
    border-bottom: 1px #ccc solid;
    padding: 10px;
  }
  .global-search-name {
    font-size: 14px;
    line-height: 20px;
    font-weight: 500;
  }
}

.CodeMirror {
  .editor-preview {
    background-color: white;
    img {
      max-width: 100%;
    }
  }
}

.answer-content {
  img {
    max-width: 100%;
  }
}

.question-content {
  img {
    max-width: 100%;
  }
}

.comment-list {
  img {
    max-width: 100%;
  }
}

.edit_comment {
  margin-top: 5px;
  padding-right: 15%;
  .CodeMirror {
    min-height: 100px;
  }
  .CodeMirror-scroll {
    min-height: 100px;
  }
}

.new_comment {
  margin-top: 5px;
  padding-right: 15%;
  .CodeMirror {
    min-height: 100px;
  }
  .CodeMirror-scroll {
    min-height: 100px;
  }
}

.page_description {
  margin: 0px;

  img,
  video,
  audio {
    max-width: 100%;
  }
}

.bg-color-none,
.breadcrumb {
  background: none !important;
}

// Replace style view qbase edit
#question_editor {
  .form-control {
    height: auto !important;
  }

  .navigator-button::after,
  .editor-button::after {
    font-family: "Font Awesome 5 Free" !important;
    font-weight: 900;
  }
  .header-menu {
    .image {
      min-width: 40px;
    }
    .buttons {
      #qeditor-save {
        white-space: nowrap;
      }
    }
  }
  .form-control {
    min-height: 35px;
  }
  .open {
    .dropdown-menu {
      display: block;
      li a {
        display: block;
        padding: 3px 20px;
        clear: both;
        font-weight: 400;
        line-height: 1.428571429;
        color: #333333;
        white-space: nowrap;
      }
      .divider {
        height: 0;
        margin: 0.5rem 0;
        overflow: hidden;
        border-top: 1px solid #e9ecef;
      }
    }
  }
  .dropdown-menu {
    .submenu-item {
      display: block;
      width: 100%;
      padding: 0.25rem 1.5rem;
      clear: both;
      font-weight: 400;
      text-align: inherit;
      white-space: nowrap;
      background-color: transparent;
      border: 0;
      a {
        color: #212529;
      }
    }
  }
}
.btn-facebook {
  color: #fff !important;
  background-color: #3b5998 !important;
  border-color: rgba(0, 0, 0, 0.2) !important;
}

// new UI material
#admin-material {
  .draw-aside {
    &.nomal-bg {
      background-color: rgb(125, 212, 32) !important;
    }
    &.exam-bg {
      background-color: #5c5a58 !important;
    }

    a,
    i,
    h3 {
      color: white;
    }
  }
  .app-bar {
    background: #efefef !important;
    position: fixed !important;
    .icon-menu {
      color: #212529 !important;
    }
  }

  .header {
    font-weight: bold;
    color: #827c7c;
    a {
      font-weight: bold;
      color: #827c7c;
    }
  }
}
.material-paginator {
  display: flex;
  padding-left: 0;
  list-style: none;
  li {
    display: block;
    .page-item {
      display: block;
      padding: 0.5rem 0.75rem;
    }
    a.page-item:hover {
      text-decoration: none;
    }
  }
}
.menu-bar-drawer {
  overflow: auto !important;
  max-height: 100vh !important;
}

// Bootstraps button forcus place by material
button:focus {
  outline: none !important;
}
.mdc-button {
  text-decoration: none !important;
  span:focus {
    outline: none !important;
  }
}
.mdc-icon-button {
  text-decoration: none !important;
  span:focus {
    outline: none !important;
  }
}

.school-user {
  .admin-users {
    .user-info {
      display: table;
      padding-left: 0;
      list-style: none;
      li.avatar {
        min-width: 50px;
      }
      li {
        display: table-cell;
        vertical-align: middle;
        .user-image {
          width: 50px;
          height: 50px;
          object-fit: cover;
        }
      }
    }
  }
}
.user-image-avatar-40 {
  object-fit: cover;
  width: 40px !important;
  height: 40px !important;
  min-width: 40px;
  min-height: 40px;
}
.user-image-avatar-50 {
  object-fit: cover;
  width: 50px !important;
  height: 50px !important;
  min-width: 50px;
  min-height: 50px;
}
.user-image-avatar-30 {
  object-fit: cover;
  width: 30px !important;
  height: 30px !important;
  min-width: 30px;
  min-height: 30px;
}
.user-image-avatar-100 {
  object-fit: cover;
  width: 100px !important;
  height: 100px !important;
  min-width: 100px;
  min-height: 100px;
}
.mdc-layout-grid {
  padding-left: 0px !important;
  padding-right: 0px !important;
}

#userListModal {
  #users_list {
    .user-detail {
      display: inline-flex !important;
      align-items: center;
      .user-image {
        margin-right: 10px;
      }
    }
  }
}
.exam-mark-new {
  padding-bottom: 0px !important;
  .bg-skill {
    background-color: #e9e9e9;
  }
  .exam-mark-form {
    overflow-y: scroll;
    height: calc(100vh - 140px);

    img {
      width: 100%;
    }
  }

  .filter-btn {
    &.disabled {
      color: #6c757d;
      cursor: auto;
      &:hover {
        text-decoration: none;
      }
    }
  }
}

.white-space-initial {
  white-space: initial !important;
}

.main_content{
  .main_content-edit{
    a{
      color: #777;
      display: inline-block;
      padding: 3px 10px;
      border: solid 1px #eee;
      background: #eee;
      border-radius: 100px;
      margin: 5px;
      font-size: 12px;
      white-space: nowrap;
      &:hover{
        text-decoration: none;
      }
    }
  }
}

.material-search-input {
  height: 36px !important;
}

.highlight {
  background-color: #222829;
  padding: 5px 5px 1px 5px;
}
.autocomplete-item {
  cursor: pointer;
}

// Mathliveスタイル
.ML__keyboard {
  z-index: 999 !important;
}
.ML__popover {
  z-index: 999 !important;
}
.ML__fieldcontainer__field {
  cursor: text !important
}
.ML__keystroke-caption {
  padding: 0 !important;
}
/* ======================================================================
/===================================================================   */
/* New Admin UI 2022 Takada CSS
/* ======================================================================
/===================================================================   */

/* sidebar */
aside{
  background-color: #476DB6 !important;
  .remove-icon {
    color: #476DB6 !important;
  }
}
.admin-edbaselogo{
  width: 172px;
  max-width: 100%;
  height: auto;
}

.sidebar-main-menu {
  display: flex;
  align-items: center; 
  margin-bottom: 0px !important;
}

/*  */

button a:hover{
  text-decoration: none;
}

.invisible-label {
  position: absolute !important;
  height: 1px;
  width: 1px;
  overflow: hidden;
  clip: rect(1px,1px,1px,1px) ;
}
.top-search .mdc-text-field,
.top-search .mdc-select__anchor{
  min-width: 100%;
  height: 3em;
  background: #FFF !important;
}

/* table */
.mdc-data-table__cell{
  white-space: normal;
  word-break: break-all;
  word-wrap: break-word;
  text-align: justify;
}

/* ユーザー写真の歪み */
.avatar-40px{
  width: 40px;
  height: 40px;
  object-fit: cover;
  object-position: center;
}

.avatar-50px{
  width: 50px;
  height: 50px;
  object-fit: cover;
  object-position: center;
}



.modal-big{
  .modal-content {
    width: 960px;
  }
}

.loading-container {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #eeeeee80;
  z-index: 2;
}

.cursor-not-allowed {
  cursor: not-allowed !important;
}

/* MDC Drawer Layout Fix - Đảm bảo layout đúng khi CSS chưa load */
.mdc-drawer.mdc-drawer--open:not(.mdc-drawer--closing) ~ .mdc-drawer-app-content {
  margin-left: 256px !important;
  margin-right: 0 !important;
}

/* Fallback cho trường hợp MDC CSS chưa load */
.mdc-drawer--open ~ .mdc-drawer-app-content {
  margin-left: 256px !important;
  margin-right: 0 !important;
}

/* Fallback đơn giản nhất - target trực tiếp */
body#admin-material .mdc-drawer-app-content {
  margin-left: 256px !important;
  margin-right: 0 !important;
}

/* Responsive: Ẩn drawer trên mobile */
@media (max-width: 768px) {
  .mdc-drawer.mdc-drawer--open:not(.mdc-drawer--closing) ~ .mdc-drawer-app-content,
  .mdc-drawer--open ~ .mdc-drawer-app-content,
  body#admin-material .mdc-drawer-app-content {
    margin-left: 0 !important;
  }
}
