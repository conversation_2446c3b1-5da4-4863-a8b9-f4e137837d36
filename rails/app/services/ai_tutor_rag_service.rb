require 'openai'

class AiTutorRagService
  include Singleton

  attr_reader :client, :index, :openai_client

  def initialize
    @default_index_name = ENV['INDEX_NAME'] || 'edbase-dev'
    @default_environment = ENV['PINECONE_ENV'] || 'us-east-1'
  end

  def get_pinecone_client(school = nil)
    api_key = get_effective_api_key(school)
    return nil unless api_key

    Pinecone.configure do |config|
      config.api_key = api_key
    end

    @clients ||= {}
    client_key = "#{school&.id}_#{api_key[0..10]}"

    @clients[client_key] ||= Pinecone::Client.new
  end

  def get_pinecone_index(school = nil)
    client = get_pinecone_client(school)
    return nil unless client

    @indices ||= {}
    index_key = "#{school&.id}_#{@default_index_name}"

    @indices[index_key] ||= client.index(@default_index_name)
  end

  def get_effective_api_key(school = nil)
    if school
      rag_config = school.ai_tutor_rag_configs.enabled.first
      if rag_config&.pinecone_api_key.present?
        return rag_config.pinecone_api_key
      end
    end

    ENV['PINECONE_API_KEY']
  end

  def get_openai_client
    @openai_client ||= OpenAI::Client.new(
      access_token: ENV['OPENAI_ACCESS_TOKEN']
    )
  end

  def self.search_materials(query:, school: nil, **options)
    instance.search_materials(
      query: query,
      school: school,
      **options
    )
  end

  def search_materials(query:, school: nil, **options)
    return { success: false, error: 'Query cannot be empty' } if query.blank?

    index = get_pinecone_index(school)
    return { success: false, error: 'Pinecone client not configured' } unless index

    begin
      top_k = options[:top_k] || 5
      namespace = options[:namespace] || 'development'
      filter = options[:filter] || {}

      query_embedding = generate_embedding(query)
      return { success: false, error: 'Failed to generate embedding' } unless query_embedding

      search_filter = build_search_filter(custom_filter: filter)

      search_results = index.query(
        vector: query_embedding,
        top_k: top_k,
        include_metadata: true,
        namespace: namespace,
        filter: search_filter
      )

      formatted_results = format_search_results(search_results)

      score_threshold = 0.01
      high_quality_results = formatted_results.select do |item|
        score = item[:score] || 0
        score >= score_threshold
      end

      {
        success: true,
        query: query,
        results: high_quality_results,
        total_found: high_quality_results.length,
        total_before_filtering: formatted_results.length,
        namespace: namespace,
        filter: filter
      }

    rescue => e
      Rails.logger.error "RAG Search Error: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")

      {
        success: false,
        error: e.message,
        query: query
      }
    end
  end

  private

  def generate_embedding(text)
    openai_client = get_openai_client
    return nil unless openai_client

    begin
      response = openai_client.embeddings(
        parameters: {
          model: 'text-embedding-ada-002',
          input: text
        }
      )

      response.dig('data', 0, 'embedding')
    rescue => e
      Rails.logger.error "Failed to generate embedding: #{e.message}"
      nil
    end
  end

  def build_search_filter(custom_filter: {})
    filter = {}

    filter.merge!(custom_filter) if custom_filter.is_a?(Hash)

    filter.empty? ? {} : filter
  end

  def format_search_results(search_results)
    return [] unless search_results&.dig('matches')

    search_results['matches'].map do |match|
      {
        id: match['id'],
        score: match['score'],
        text: match.dig('metadata', 'text'),
        metadata: match['metadata']&.except('text'),
        source: match.dig('metadata', 'source'),
        lesson_id: match.dig('metadata', 'lesson_id'),
        school_id: match.dig('metadata', 'school_id')
      }
    end.compact
  end
end
