class DebugLogger
  CACHE_PREFIX = "playground_debug"
  MAX_LOGS = 50
  CACHE_EXPIRY = 2.hours
  DUPLICATE_THRESHOLD = 1.second

  class << self
    def add_debug_info(session, type, message, data = nil, user_id = nil)
      user_id = resolve_user_id(user_id)
      cache_key = build_cache_key(user_id)
      
      log_entry = build_log_entry(type, message, data)

      begin
        cached_logs = Rails.cache.read(cache_key) || []
        
        unless duplicate_exists?(cached_logs, log_entry)
          cached_logs << log_entry
          final_logs = cached_logs.last(MAX_LOGS)
          Rails.cache.write(cache_key, final_logs, expires_in: CACHE_EXPIRY)
        end
      rescue => e
        Rails.logger.error "🔍 [DEBUG_CACHE] Cache error: #{e.message}"
      end
    end

    def get_debug_info(user_id)
      cache_key = build_cache_key(user_id)

      begin
        Rails.cache.read(cache_key) || []
      rescue => e
        Rails.logger.error "🔍 [DEBUG_CACHE] Cache read error: #{e.message}"
        []
      end
    end

    def clear_debug_info(user_id)
      cache_key = build_cache_key(user_id)
      
      begin
        Rails.cache.delete(cache_key)
      rescue => e
        Rails.logger.error "🔍 [DEBUG_CACHE] Cache clear error: #{e.message}"
      end
    end

    private

    def resolve_user_id(user_id)
      user_id || 
        Thread.current[:current_user]&.id || 
        Thread.current[:debug_user_id] || 
        'anonymous'
    end

    def build_cache_key(user_id)
      "#{CACHE_PREFIX}_#{user_id}"
    end

    def build_log_entry(type, message, data)
      {
        id: SecureRandom.hex(8),
        timestamp: Time.current,
        type: type,
        message: message,
        data: data
      }
    end

    def duplicate_exists?(cached_logs, new_entry)
      cached_logs.find do |log|
        log[:type] == new_entry[:type] &&
        log[:message] == new_entry[:message] &&
        (Time.current - log[:timestamp]) < DUPLICATE_THRESHOLD
      end
    end
  end
end
