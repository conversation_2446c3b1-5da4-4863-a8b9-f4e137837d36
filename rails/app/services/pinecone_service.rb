class PineconeService
  def initialize(ai_tutor_agent)
    @ai_tutor_agent = ai_tutor_agent
    @rag_config = ai_tutor_agent.ai_tutor_rag_configs.first
    @client = nil
    @index = nil
  end

  def self.for_agent(ai_tutor_agent)
    new(ai_tutor_agent)
  end

  def client
    @client ||= begin
      require 'pinecone'

      Pinecone.configure do |config|
        config.api_key = effective_api_key
      end

      Pinecone::Client.new
    end
  end

  def index
    @index ||= client.index(index_name)
  end

  def index_name
    ENV['INDEX_NAME'] || 'edbase-dev'
  end

  def namespace
    @rag_config&.default_namespace || 'development'
  end

  def effective_api_key
    @rag_config&.effective_pinecone_api_key || ENV['PINECONE_API_KEY']
  end

  def delete_by_metadata(filter)
    begin
      Rails.logger.info "Deleting vectors from Pinecone namespace: #{namespace} with filter: #{filter}"
      index.delete(filter: filter, namespace: namespace)
      Rails.logger.info "Successfully deleted vectors with filter: #{filter}"
      true
    rescue => e
      Rails.logger.error "Failed to delete vectors by metadata: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  # Delete vectors by IDs (much simpler than metadata filter)
  def delete_vectors(vector_ids)
    return true if vector_ids.empty?

    begin
      Rails.logger.info "Deleting #{vector_ids.length} vectors from Pinecone namespace: #{namespace}"

      # Simple delete all at once
      index.delete(ids: vector_ids, namespace: namespace)

      Rails.logger.info "Successfully deleted #{vector_ids.length} vectors from Pinecone"
      true
    rescue => e
      Rails.logger.error "Failed to delete vectors from Pinecone: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      false
    end
  end

  # Query vectors
  def query(vector, top_k: 10, filter: nil, include_metadata: true)
    begin
      response = index.query(
        vector: vector,
        top_k: top_k,
        filter: filter,
        include_metadata: true,
        namespace: namespace
      )
      response['matches'] || []
    rescue => e
      Rails.logger.error "Failed to query Pinecone: #{e.message}"
      []
    end
  end

  # Get index stats
  def stats
    begin
      index.describe_index_stats
    rescue => e
      Rails.logger.error "Failed to get Pinecone stats: #{e.message}"
      {}
    end
  end

  # Generate embeddings using RubyLLM (simplified)
  def generate_embeddings(texts)
    return [] if texts.empty?

    begin
      config = RubyLLM::Configuration.new
      config.openai_api_key = ENV['OPENAI_ACCESS_TOKEN']
      context = RubyLLM::Context.new(config)

      texts.map do |text|
        embedding = RubyLLM.embed(text, context: context)
        embedding.vectors
      end
    rescue => e
      Rails.logger.error "Failed to generate embeddings: #{e.message}"
      []
    end
  end

  def configured?
    effective_api_key.present?
  end
end
