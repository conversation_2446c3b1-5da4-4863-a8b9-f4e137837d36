include ActionView::Helpers::SanitizeHelper
include ActionView::Helpers::TextHelper

module LessonChat
  def self.chat_steam(messages, object, process_id, system_message_content: nil)
    unless object.targetable_type == "Lesson"
      Rails.logger.error "LessonChat only supports Lesson objects, got: #{object.targetable_type}"
      return
    end

    Rails.cache.write(process_id, true)
    channel = "user:#{object.to_user_id}:#{object.targetable_type}:#{object.targetable_id}:chats"

    LessonChat.request_custom_ai(messages, object, system_message_content: system_message_content) do |content, reasoning_content, chunk|
      if chunk.content.present? || chunk.reasoning_content.present?
        is_generating = Rails.cache.read(process_id)
        if is_generating
          ActionCable.server.broadcast channel, {
            message_id: object.id,
            content: ApplicationController.helpers.emojify(UtilityHelper.markdown_to_html UtilityHelper.replace_outside_code_tags(content)).html_safe,
            reasoning_content: reasoning_content,
            finished: false
          }
        else
          object.update(content: content, reasoning_content: reasoning_content)
          break
        end
      end
    end

    is_generating = Rails.cache.read(process_id)

    ActionCable.server.broadcast channel, {
      message_id: object.id,
      content: "Finished",
      finished: true
    }

    is_generating
  end

  def self.request_custom_ai(messages, object = nil, system_message_content: nil)
    # Only support Lesson objects
    unless object&.targetable_type == "Lesson"
      yield "エラー: Lessonオブジェクトのみサポートされています。", "エラー: Lessonオブジェクトのみサポートされています。"
      return
    end

    lesson = Lesson.find(object.targetable_id)
    school = lesson.school
    ai_platform_school = school.ai_platform_school

    if ai_platform_school.blank? || ai_platform_school.api_key.blank? || ai_platform_school.model.blank?
      yield "AI model not configured yet.", "AI model not configured yet."
      return
    end

    config = RubyLLM::Configuration.new
    config.anthropic_api_key = ai_platform_school.api_key
    config.openai_api_key = ai_platform_school.api_key
    config.gemini_api_key = ai_platform_school.api_key
    config.deepseek_api_key = ai_platform_school.api_key
    model = ai_platform_school.model

    chat = RubyLLM.chat(model: model, context: RubyLLM::Context.new(config))

    # Set current user in thread for function calling tools
    user_id = object&.to_user_id
    if user_id
      current_user = User.find(user_id) rescue nil
      Thread.current[:current_user] = current_user
    end

    # Add function calling tools based on agent type (lesson only)
    add_function_calling_tools(chat, school, 'lesson')

    if system_message_content.present?
      system_message = RubyLLM::Message.new(
        role: :system,
        content: system_message_content
      )

      chat.add_message(system_message)
    end

    # Get RAG contexts
    last_message = messages.last
    last_message_content = last_message.content

    # Get lesson-specific context (from lesson materials)
    lesson_context = get_lesson_rag_context(last_message_content, lesson, school)

    # Get agent-specific context (from AI Tutor Sources)
    lesson_agent = school.ai_tutor_agents.find_by(agent_type: 'lesson')
    agent_context = lesson_agent ? get_agent_rag_context(last_message_content, lesson_agent, school) : ""

    # Combine contexts
    combined_context = [lesson_context, agent_context].reject(&:blank?).join("\n\n---\n\n")

    # Get system prompt from AiTutorPrompt or use default
    system_prompt_content = get_lesson_system_prompt(lesson, school, last_message_content, combined_context)
    system_message = RubyLLM::Message.new(
      role: :system,
      content: system_prompt_content
    )

    chat.add_message(system_message)

    messages.each do |message|
      attachments = school.using_deepseek? ? [] : message.attachments.map(&:url)

      chat_message = RubyLLM::Message.new(
        role: message.bot? ? :assistant : :user,
        content: RubyLLM::Content.new(
          message.content,
          attachments
        )
      )

      chat.add_message(chat_message)
    end

    content = ""
    reasoning_content = ""
    chat.complete do |chunk|
      if chunk.content
        content += chunk.content

        yield content, reasoning_content, chunk
      end

      if chunk.reasoning_content
        reasoning_content += chunk.reasoning_content

        yield content, reasoning_content, chunk
      end
    end
  rescue => e
    Rails.logger.error "Error in LessonChat.request_custom_ai: #{e.message}"
    puts "Error in LessonChat.request_custom_ai: #{e.message}"
    yield "エラーが発生しました。", "エラーが発生しました。"
  ensure
    Thread.current[:current_user] = nil

    return unless object
    return "" if content.blank?

    attachments = charts_from_content(content)
    if attachments.present?
      md = attachments.map { |a| "![draw](#{a.url})" }.join("\n")
      yield content + "\n" + md, md
    end

    if attachments.present?
      object.update(content: content, reasoning_content: reasoning_content, attachment_ids: attachments.map(&:id))
    else
      object.update(content: content, reasoning_content: reasoning_content)
    end

    content
  end

  # Add function calling tools from database for specific agent
  def self.add_function_calling_tools(chat, school, agent_type)
    begin
      # Find the specific agent
      agent = school.ai_tutor_agents.find_by(agent_type: agent_type)
      unless agent
        Rails.logger.warn "No agent found for type: #{agent_type}"
        return
      end

      # Add only agent-specific tools from database
      agent.ai_tutor_tools.enabled.each do |tool|
        begin
          Rails.logger.info "Adding agent tool: #{tool.tool_name} for agent: #{agent_type}"

          # Create dynamic tool class from tool definition
          dynamic_tool_class = create_dynamic_tool_class(tool)
          if dynamic_tool_class
            chat.with_tool(dynamic_tool_class)
            Rails.logger.info "Successfully added tool: #{tool.tool_name}"
          else
            Rails.logger.warn "Failed to create dynamic tool class for: #{tool.tool_name}"
          end
        rescue => e
          Rails.logger.error "Failed to add tool #{tool.tool_name}: #{e.message}"
          Rails.logger.error "Error class: #{e.class}"
          Rails.logger.error "Backtrace: #{e.backtrace.first(5).join("\n")}"
        end
      end

      Rails.logger.info "Added #{agent.ai_tutor_tools.enabled.count} tools for agent: #{agent_type}"
    rescue => e
      Rails.logger.error "Error in add_function_calling_tools: #{e.message}"
      Rails.logger.error e.backtrace.join("\n")
      raise e
    end
  end

  # Create dynamic tool class from custom tool definition
  def self.create_dynamic_tool_class(custom_tool)
    return nil if custom_tool.function_definition.blank?

    # Extract method name and parameters from function definition
    method_info = parse_function_definition(custom_tool.function_definition)
    return nil unless method_info

    # Create a new class that inherits from RubyLLM::Tool
    tool_class = Class.new(RubyLLM::Tool) do
      # Combine description with when_to_use for better LLM context
      full_description = custom_tool.description
      if custom_tool.when_to_use.present?
        full_description += "\n使用場面: #{custom_tool.when_to_use}"
      end
      description full_description

      # Add parameters based on function definition (excluding user_id)
      method_info[:params].each do |param_name, param_info|
        next if param_name == 'user_id' # Skip user_id as we use current_user
        param param_name.to_sym,
              desc: param_info[:description] || param_name.to_s,
              required: param_info[:required] != false
      end

      # Store function definition and method name
      @function_definition = custom_tool.function_definition
      @method_name = method_info[:method_name]

      define_method :execute do |**params|
        begin
          # Create a safe execution context
          execution_context = create_execution_context

          # Execute the function definition in the context
          execution_context.instance_eval(self.class.instance_variable_get(:@function_definition))

          # Call the defined method
          method_name = self.class.instance_variable_get(:@method_name)
          if execution_context.respond_to?(method_name, true)
            execution_context.send(method_name, **params)
          else
            { error: "Method #{method_name} not found in function definition" }
          end
        rescue => e
          Rails.logger.error "Custom tool execution error: #{e.message}"
          { error: "Function execution failed: #{e.message}" }
        end
      end

      private

      define_method :create_execution_context do
        # Create a context with access to Rails models and helpers
        context = Object.new

        # Add Rails models
        context.define_singleton_method(:User) { ::User }
        context.define_singleton_method(:School) { ::School }
        context.define_singleton_method(:Lesson) { ::Lesson }
        context.define_singleton_method(:Exam) { ::Exam }
        context.define_singleton_method(:Rails) { ::Rails }

        # Add current_user method
        context.define_singleton_method(:current_user) { Thread.current[:current_user] }

        # Add helper methods
        context.define_singleton_method(:days) { |n| n.days }
        context.define_singleton_method(:weeks) { |n| n.weeks }
        context.define_singleton_method(:months) { |n| n.months }
        context.define_singleton_method(:ago) { |duration| duration.ago }

        context
      end
    end

    # Set class name for debugging
    tool_class.define_singleton_method(:name) { "CustomTool_#{custom_tool.tool_name}" }

    tool_class
  rescue => e
    Rails.logger.error "Failed to create dynamic tool class for #{custom_tool.tool_name}: #{e.message}"
    nil
  end

  # Parse function definition to extract method name and parameters
  def self.parse_function_definition(code)
    # Extract method definition line (with optional parentheses)
    method_match = code.match(/def\s+(\w+)(?:\s*\(([^)]*)\))?/)
    return nil unless method_match

    method_name = method_match[1]
    params_string = method_match[2] || ""

    # Parse parameters
    params = {}
    if params_string.present?
      params_string.split(',').each do |param|
        param = param.strip
        if param.include?(':')
          # Keyword parameter
          parts = param.split(':')
          param_name = parts[0].strip
          default_value = parts[1]&.strip
          params[param_name] = {
            description: param_name.humanize,
            required: !default_value || default_value == 'nil'
          }
        else
          # Regular parameter
          param_name = param.gsub(/[^a-zA-Z0-9_]/, '')
          params[param_name] = {
            description: param_name.humanize,
            required: true
          }
        end
      end
    end

    {
      method_name: method_name,
      params: params
    }
  rescue => e
    Rails.logger.error "Failed to parse function definition: #{e.message}"
    nil
  end

  # Get system prompt for lesson from AiTutorPrompt
  def self.get_lesson_system_prompt(lesson, school, user_input, pinecone_context)
    # Get custom system prompt from database
    custom_prompt = AiTutorPrompt.system_prompt_for(school, 'lesson')

    if custom_prompt.present?
      # Use custom prompt with variable substitution
      prompt_model = school.ai_tutor_prompts.enabled.find_by(prompt_type: 'lesson')
      variables = {
        'lesson_title' => lesson.name,
        'lesson_body' => lesson.md_body || lesson.body || '',
        'materials' => pinecone_context,
        'user_input' => user_input,
        'user_name' => Thread.current[:current_user]&.name || 'ユーザー',
        'lesson_id' => lesson.id.to_s
      }

      return prompt_model.render_content(variables)
    else
      # Use default prompt with Pinecone context
      return <<~PROMPT
        あなたはAIアシスタントです。生徒の質問に答えてください。
        画像に関する質問がある場合は、提供された画像の説明を参照してください。
        Pineconeのコンテキスト情報は補足的に使用し、質問に直接関係ない場合は無視してください。
        常に簡潔で的確な回答を心がけてください。

        現在のレッスン：#{lesson.name}

        【関連教材】
        #{pinecone_context}

        【生徒の質問】
        #{user_input}
      PROMPT
    end
  end

  # Get lesson RAG context (for lesson-specific data)
  def self.get_lesson_rag_context(query, lesson, school)
    return "" if query.blank?

    begin
      # Find lesson agent for this school
      lesson_agent = school.ai_tutor_agents.find_by(agent_type: 'lesson')
      return "" unless lesson_agent

      # Get RAG config for the lesson agent
      rag_config = lesson_agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      # Use AiTutorRagService to search for lesson-specific materials
      search_params = rag_config.search_params_for({
        lesson_id: lesson.id
      })

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        # Extract text content from search results
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No lesson RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting lesson RAG context: #{e.message}"
      return ""
    end
  end

  # Get agent RAG context (for AI Tutor Sources: text, web, qa)
  def self.get_agent_rag_context(query, agent, school)
    return "" if query.blank?

    begin
      # Get RAG config for the agent
      rag_config = agent.ai_tutor_rag_configs.first
      return "" unless rag_config&.enabled?

      # Use AiTutorRagService to search for agent sources (no lesson filter)
      search_params = rag_config.search_params_for({})

      result = AiTutorRagService.search_materials(
        query: query,
        school: school,
        **search_params
      )

      if result[:success] && result[:results].present?
        # Extract text content from search results
        contexts = result[:results].map do |item|
          item[:text] || item['text']
        end.compact

        return contexts.join("\n\n")
      else
        Rails.logger.info "No agent RAG context found for query: #{query}"
        return ""
      end
    rescue => e
      Rails.logger.error "Error getting agent RAG context: #{e.message}"
      return ""
    end
  end

  def self.charts_from_content(content)
    # Simplified chart detection - no external service call
    # This could be enhanced to use a local Python execution service if needed
    content.split("```").select do |child|
      text = child.strip
      text.starts_with?("python") && text.ends_with?("show()") && text.include?("import matplotlib.pyplot")
    end.map do |child|
      # For now, just return nil since we're not calling external service
      # This could be implemented with a local service later
      nil
    end.compact
  end
end
