<% simple_mde_class = "simple-mde-editor-lesson" %>
<% simple_mde_class_description = "simple-mde-editor-lesson-description" %>

<%= form_for [:admin, @school, @goal, @course, lesson], multipart: true, remote: true do |f| %>

<%= f.hidden_field :video_type %>

<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-4">
      <a href="<%= url_for([:admin, @school, @course, :lessons]) %>" type="button"
        class="mdc-button mdc-button--outlined me-2">
        <i class="material-icons">west</i>
      </a>
      <span id="lesson_name_container"><%= lesson.name %></span>
    </div>
    <div class="mdc-layout-grid__cell--span-8 text-end">
      <div class="d-flex align-items-center justify-content-end">
        <% if @school.use_vector_button %>
          <!-- Vector progress indicator -->
          <div id="vector-generation-progress" class="me-2" style="min-width: 200px; text-align: left;">
            <div class="mb-1">
              <div class="progress" style="height: 10px; width: 100%;">
                <div id="vector-progress-bar" class="progress-bar progress-bar-striped progress-bar-animated" 
                    role="progressbar" style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
              </div>
            </div>
            <div class="d-flex align-items-center">
              <span id="vector-progress-percentage" class="me-2 text-muted">0%</span>
              <span id="vector-status-message" class="text-truncate">ベクター生成を準備中...</span>
            </div>
          </div>

          <label class="mdc-typography--headline7 save-status text-success me-2 d-none"></label>

          <div class="text-muted me-2" id="vector-generated-timestamp" style="min-width: 60px; text-align: left;">
            <% if lesson.vector_generated_at.present? %>
              <small>最終保存: <%= lesson.vector_generated_at.strftime("%Y/%m/%d %H:%M") %></small>
            <% end %>
          </div>
          <!-- Vector buttons -->
          <button type="button" class="mdc-button mdc-button--outlined me-2" id="generate-vector-btn" data-lesson-id="<%= lesson.id %>">
            <span class="mdc-button__ripple"></span>
            <i class="material-icons mdc-button__icon me-1">auto_awesome</i>
            <span class="mdc-button__label">ベクター生成(Python)</span>
          </button>

          <button type="button" class="mdc-button mdc-button--outlined me-2" id="generate-vector-rails-btn" data-lesson-id="<%= lesson.id %>">
            <span class="mdc-button__ripple"></span>
            <i class="material-icons mdc-button__icon me-1">memory</i>
            <span class="mdc-button__label">ベクター生成 (Rails)</span>
          </button>
        <% end %>

        <!-- Submit button -->
        <%= f.submit class: "mdc-button mdc-button--raised", onclick: "onSumbmitClick()", id: "submit_lesson_btn"%>
      </div>
    </div>
  </div>
</div>

<% if lesson.errors.any? %>
<div class="mdc-layout-grid py-3">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell" id="error_explanation">
      <ul class="mb-0">
        <% lesson.errors.full_messages.each do |message| %>
        <li><%= message %></li>
        <% end %>
      </ul>
    </div>
  </div>
</div>
<% end %>

<div class="mdc-layout-grid pt-0">
  <div class="mdc-layout-grid__inner">
    <div class="mdc-layout-grid__cell--span-8">
      <div class="mdc-card mdc-card--outlined px-3 pt-4">
        <label class="mdc-text-field mdc-text-field--outlined">
          <span class="mdc-notched-outline">
            <span class="mdc-notched-outline__leading"></span>
            <span class="mdc-notched-outline__notch">
              <span class="mdc-floating-label">レッスン名</span>
            </span>
            <span class="mdc-notched-outline__trailing"></span>
          </span>
          <%= f.text_field :name, class: "mdc-text-field__input lesson_name_input", autofocus: true, placeholder: "Empty" %>
        </label>

        <section class="form-group my-4">
          <div class="d-flex flex-wrap">
            <div class="mdc-form-field me-4">
              <div class="mdc-radio">

                <%= f.radio_button :lesson_type, :video, checked: lesson.video?, class: "mdc-radio__native-control", id: "lesson_type_video" %>
                <div class="mdc-radio__background">
                  <div class="mdc-radio__outer-circle"></div>
                  <div class="mdc-radio__inner-circle"></div>
                </div>
                <div class="mdc-radio__ripple"></div>
              </div>
              <label for="lesson_type_video" class="mb-0">ビデオレッスン</label>
            </div>
            <div class="mdc-form-field me-4">
              <div class="mdc-radio">
                <%= f.radio_button :lesson_type, :text, checked: lesson.text?, class: "mdc-radio__native-control", id: "lesson_type_text" %>
                <div class="mdc-radio__background">
                  <div class="mdc-radio__outer-circle"></div>
                  <div class="mdc-radio__inner-circle"></div>
                </div>
                <div class="mdc-radio__ripple"></div>
              </div>
              <label for="lesson_type_text" class="mb-0">テキストレッスン</label>
            </div>
            <div class="mdc-form-field me-4">
              <div class="mdc-radio">
                <%= f.radio_button :lesson_type, :exam, checked: lesson.exam?, class: "mdc-radio__native-control", id: "lesson_type_exam" %>
                <div class="mdc-radio__background">
                  <div class="mdc-radio__outer-circle"></div>
                  <div class="mdc-radio__inner-circle"></div>
                </div>
                <div class="mdc-radio__ripple"></div>
              </div>
              <label for="lesson_type_exam" class="mb-0">テストレッスン</label>
            </div>
            <div class="mdc-form-field me-4">
              <div class="mdc-radio">
                <%= f.radio_button :lesson_type, :live, checked: lesson.live?, class: "mdc-radio__native-control", id: "lesson_type_live" %>
                <div class="mdc-radio__background">
                  <div class="mdc-radio__outer-circle"></div>
                  <div class="mdc-radio__inner-circle"></div>
                </div>
                <div class="mdc-radio__ripple"></div>
              </div>
              <label for="lesson_type_exam" class="mb-0">Live</label>
            </div>
          </div>
        </section>
        <div id="lesson_type_video_block" style="display: <%= lesson.video? || lesson.live? ? 'block' : 'none' %>;">
          <div class="mdc-layout-grid__inner">
            <div class="mdc-layout-grid__cell--span-12">
              <div class="mdc-tab-bar" role="tablist">
                <div class="mdc-tab-scroller">
                  <div class="mdc-tab-scroller__scroll-area">
                    <div class="mdc-tab-scroller__scroll-content">
                      <a class="mdc-tab mdc-tab--active youtube-tab">
                        <span class="mdc-tab__content">
                          <span class="mdc-tab__text-label">Youtube or Vimeo</span>
                          <span class="badge bg-success small me-2 youtube-tab-active-bar">アクティブ</span><br>
                        </span>
                        <span class="youtube-tab-indicator mdc-tab-indicator mdc-tab-indicator--active">
                          <span class="mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                        </span>
                        <span class="mdc-tab__ripple"></span>
                      </a>
                      <a class="mdc-tab mdc-tab vimeo-tab">
                        <span class="mdc-tab__content">
                          <span class="mdc-tab__text-label">ビデオのアップロード</span>
                          <span class="badge bg-success small me-2 vimeo-tab-active-bar">アクティブ</span><br>
                        </span>
                        <span class="vimeo-tab-indicator mdc-tab-indicator">
                          <span class=" mdc-tab-indicator__content mdc-tab-indicator__content--underline"></span>
                        </span>
                        <span class="mdc-tab__ripple"></span>
                      </a>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="admin-youtube-video mdc-layout-grid__cell--span-12">
            <div class="d-flex justify-content-between align-items-center">
              <label class="mdc-text-field mdc-text-field--outlined mt-3" style="width: 100%">
                <span class="mdc-notched-outline">
                  <span class="mdc-notched-outline__leading"></span>
                  <span class="mdc-notched-outline__notch">
                    <span class="mdc-floating-label">Youtubeがある場合はidを追加する</span>
                  </span>
                  <span class="mdc-notched-outline__trailing"></span>
                </span>
                <%= f.text_field :video, class: "mdc-text-field__input" %>
              </label>
              <div class="ms-2" style="width: 100px">
                <button type="button" class="btn btn-outline-primary btn-sm video_confim">動画確認</button>
              </div>
            </div>
            <%= image_tag "video_icon.png", class: "mb-4 img-fluid no_image_video", width: "100%", height: "450px" %>
            <iframe width="100%" height="450px" class="youtube-video"
              style="<%= 'display: none' unless lesson.video.present?%>" src=""
              title="YouTube video player" frameborder="0"
              allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
              allowfullscreen></iframe>
          </div>
          <div class="admin-vimeo-video mdc-layout-grid__cell--span-12" style="display: none">
            <div class="progress progress-container collapse mb-2 mt-2">
              <div id="upload-progress-bar" class="progress-bar progress-bar-success progress-bar-striped active"
                role="progressbar" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100">
                <span class="sr-only"></span>
              </div>
              <div class="cancel-upload-text">
                <a href="#" class="cancel-upload">アップロードをキャンセルする</a>
              </div>
            </div>
            <div class="mdc-text-field-helper-line">
              <div class="mdc-text-field-helper-text">※Youtubeがある場合はIDを追加する。</div>
            </div>
            <div class="w-100">
              <div id="results">
                <% if f.object.vimeo.present? %>
                <%= f.object.vimeo.present? ? "#{f.object.vimeo}.mp4" : "" %>
                <a href="#" class="remove-video">この動画を削除する</a>
                <% end %>
              </div>
              <%= f.hidden_field :vimeo_complete_uri %>
            </div>
            <div id="vimeo-iframe mt-3">
              <div class="vimeo-processing" style="display: none">
                <%= image_tag "optimizing_video.png", width: "100%"%>
                <p>
                  ビデオの処理中です（ビデオの長さの2倍くらいの時間がかかります）<br>
                  <span class="vimeo-processing-log badge rounded-pill bg-secondary"></span>
                </p>
              </div>
              <iframe src="https://player.vimeo.com/video/<%= lesson.vimeo %>?h=9f19d6f0ce"
                style="<%= 'display: none' unless lesson.vimeo.present?%>" class="vimeo-url" width="100%" height="450px"
                frameborder="0" title="{video_title}" webkitallowfullscreen mozallowfullscreen allowfullscreen>
              </iframe>
              <input type='button' value="更新して結果を確認する" class="mdc-button mdc-button--raised refresh-btn" style="display: none"/>
            </div>
            <div class="mt-3">
              <%= f.label "ビデオのアップロード" %>
              <p>Skillhubのみで閲覧できる動画はこちらでアップロード</p>
                <div id="dropzone" style="<%= lesson.vimeo.present? ? 'display: none': '' %>">
                  <%= image_tag "video_icon.png", class: "mb-4 img-fluid no_image_vimeo", width: "100%", height: "450px"  %>
                </div>
                <label class="mdc-button mdc-button--raised upload-videmo w-100">
                ファイルを選択&hellip;
                <input id="browse" type="file" style="display: none;" accept="video/*"
                  data-upload-url="<%= polymorphic_path([:vimeo, :admin, @school, @goal, @course, :lessons]) %>?lesson_id=<%= f.object.id %>"
                  data-check-url="<%= polymorphic_path([:check_vimeo, :admin, @school, @goal, @course, :lessons]) %>"
                >
              </label>
            </div>
          </div>
        </div>
        <div class="mt-2" id="lesson_type_text_block" style="display: <%= lesson.text? ? 'block' : 'none' %>;">
          <div class="d-flex w-100 justify-content-between">
            <%= f.label "本文" %>
            <div class="d-flex align-items-center">
              <%= f.radio_button :body_type, 0, class: 'body_type_md' %> SimpleMde<br />
              <%= f.radio_button :body_type, 1, class: 'body_type_ck ms-2' %> CkEditor<br />
            </div>
          </div>
          <div class="md_body_editor">
            <%= f.text_area :md_body, id: :blog_body, class: "#{simple_mde_class} " %>
          </div>
          <div class="ck_body_editor custom-ckeditor-box">
            <%= f.text_area :ck_body, id: "lesson-setting-ck", class: "custom-ckeditor", row: 10 %>
          </div>

        </div>
        <div class="mt-2" id="lesson_type_exam_block" style="display: <%= lesson.exam? ? 'block' : 'none' %>;">
          <div class="w-100">
            <div id="exam-data" style="display: <%= @exam ? 'block' : 'none' %>;">
              <%= render 'exam_data'%>
            </div>
            <div id="exam-list" style="display: <%= @exam ? 'none' : 'block' %>;">
              <div id="exam-list-table">
                <%= render 'exam_list'%>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="mdc-layout-grid__cell--span-4">
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mdc-layout-grid w-100 py-0">
          <div class="mdc-layout-grid__inner">
            <div class="mdc-layout-grid__cell--span-5">
              <label class="mdc-typography--subtitle1">公開/未公開</label>
            </div>
            <div class="mdc-layout-grid__cell--span-7 text-end">
              <a class="mdc-button mdc-button--outlined mdc-button--toggle-menu">
                プレビュー
              </a>
              <div class="mdc-menu mdc-menu-surface" style="width: 140px">
                <ul class="mdc-list" role="menu" aria-hidden="true" aria-orientation="vertical" tabindex="-1">
                  <%= link_to "/admin/schools/#{lesson.course.school.id}/courses/#{lesson.course.id}/lessons/#{lesson.id}/classroom_preview", class: "mdc-list-item", target: "_blank" do %>
                  <span class="mdc-button__ripple"></span>
                  <span class="mdc-button__label">クラスルーム</span>
                  <% end %>

                  <% if @course.publish_global %>
                    <%= link_to [@course, lesson], class: "mdc-list-item", target: "_blank" do %>
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">グローバル</span>
                    <% end %>
                  <% else %>
                    <div class="mdc-list-item bg-secondary text-white bg-gradient cursor-not-allowed">
                    <span class="mdc-button__ripple"></span>
                    <span class="mdc-button__label">グローバル</span>
                    </div>
                  <% end %>
                </ul>
              </div>
            </div>
          </div>
        </div>
        <div class="mdc-form-field">
          <div class="mdc-radio">
            <%= f.radio_button :published, "1", checked: lesson.published_at.present?, class: "mdc-radio__native-control" %>
            <div class="mdc-radio__background">
              <div class="mdc-radio__outer-circle"></div>
              <div class="mdc-radio__inner-circle"></div>
            </div>
            <div class="mdc-radio__ripple"></div>
          </div>
          <label class="mt-2" for="lesson_published_1">公開
            <% if lesson.published_at.present? %>
            <span class="ms-3"><small><%= nichiji lesson.published_at %></small></span>
            <% end %>
          </label>
        </div>
        <div class="mdc-form-field mb-3">
          <div class="mdc-radio">
            <%= f.radio_button :published, "0", checked: !lesson.published_at.present?, class: "mdc-radio__native-control" %>
            <div class="mdc-radio__background">
              <div class="mdc-radio__outer-circle"></div>
              <div class="mdc-radio__inner-circle"></div>
            </div>
            <div class="mdc-radio__ripple"></div>
          </div>
          <label class="mt-2" for="lesson_published_0">未公開</label>
        </div>

        <div class="">
          <%= render "/admin/common/schedule_published_setting", schedule_published_at: lesson.schedule_published_at %>
        </div>
      </div>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <%= f.label "概要" %>
        <span class="mdc-text-field__resizer">
          <%= f.text_area :description, class: "#{simple_mde_class_description}", rows: 4 %>
        </span>
      </div>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mdc-layout-grid w-100 py-0">
          <div class="mdc-layout-grid__inner">
            <div class="mdc-layout-grid__cell--span-3 view-image">
              <% if lesson.image? %>
                <%= image_tag lesson.image_url, class: "show-image", style: "width: 60px; height: 40px;" %>
              <% else %>
                <%= image_tag "", class: "show-image", style: "" %>
              <% end %>
            </div>
            <div class="mdc-layout-grid__cell--span-9 float-left">
              <%= f.file_field :image, accept: Settings.upload_type.image %>
            </div>
          </div>
        </div>
        <%= f.hidden_field :image_cache %>
        <a href="javascript:void(0);" class="delete-image" , style="<%='display: none;' unless lesson.image? %>">
          画像を削除する</a>
      </div>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <%= f.label "目次" %>
        <div class="mt-3">
          <%= f.check_box :subtitle_h2 %>
          <%= f.label "h2の目次" %>
          <br />
          <div class="subtitle_h3">
            <%= f.check_box :subtitle_h3 %>
            <%= f.label "h3の目次" %>
          </div>
        </div>
      </div>
      <div id="previewable-setting" class="mdc-card mdc-card--outlined p-3 mb-3">
        <%= f.label "プレビューを許可する" %>
        <div class="mt-3">
          <%= f.check_box :is_previewable %>
          <%= f.label "許可" %>
        </div>
      </div>
      <div id="teacher-setting" class="mdc-card mdc-card--outlined p-3 mb-3">
        <%= f.label "先生設定" %>
        <div class="mt-3">
          <%= f.collection_check_boxes(:teacher_ids, @course.teachers, :id, :name) do |b| %>
            <div><%= b.label { b.check_box + b.text } %></div>
          <% end %>
        </div>
      </div>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mt-3">
          <div class="mdc-layout-grid w-100 py-0 mb-2">
            <div class="mdc-layout-grid__inner">
              <div class="mdc-layout-grid__cell--span-5">
                <label class="mdc-typography--headline5"><%= t("materials.index") %></label>
              </div>
              <div class="mdc-layout-grid__cell--span-7 text-end">
                <%= form_for lesson, url: admin_school_lesson_materials_path(@school, lesson), method: :post do |form| %>
                  <%= link_to "素材をアップロード", "#", class: "mdc-button mdc-button--raised material-upload-btn"  %>
                  <%= form.file_field :materials, multiple: true, direct_upload: true, class: "d-none auto-submit-upload material-upload" %>
                <% end %>
              </div>
            </div>
          </div>
          <div class="upload-progress"></div>
          <hr class="mt-2 mb-2">
          <div id="materials_list">
            <%= render partial: "material", collection: @materials %>
          </div>
        </div>
      </div>

      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <%= render "form_ai_question", f: f, lesson: lesson %>
      </div>

      <% if lesson.id.present? %>
      <div class="mdc-card mdc-card--outlined p-3 mb-3">
        <div class="mdc-layout-grid w-100 py-0 mb-2">
          <div class="mdc-layout-grid__inner">
            <div class="mdc-layout-grid__cell--span-8">
              <label class="mdc-typography--headline5">レッスン一覧</label>
            </div>
          </div>
        </div>
        <ul class="mb-0">
          <% @course_lessons.each do |course_lesson| %>
          <% lesson = course_lesson.lesson %>
          <li class="mb-2">
            <%= link_to_unless_current lesson.name, [:edit, :admin, @school, @course, lesson] %>
          </li>
          <% end %>
        </ul>
      </div>
      <% end %>
    </div>
  </div>
</div>
<% end %>

<div class="modal fade" id="addAiChatQuestionModal" tabindex="-1" role="dialog" aria-labelledby="addAiChatQuestionModalLabel" aria-hidden="true">
  <div class="modal-dialog" role="document">
    <div class="modal-content">
      <div class="modal-header">
        <h5 class="modal-title" id="addAiChatQuestionModalLabel">AIよくある質問を追加</h5>
        <button type="button" class="close" data-dismiss="modal" aria-label="Close">
          <span aria-hidden="true">&times;</span>
        </button>
      </div>
      <div class="modal-body" id="formAiChatQuestionModal">
        <%= render "form_ai_chat_question_modal", ai_chat_question: AiChatQuestion.new, lesson: lesson %>
      </div>
    </div>
  </div>
</div>

<script>
  var isEditorChanged = false
  $("#lesson_name").on("change", () => {
    isEditorChanged = true
  })


  $("#vector-generation-progress").addClass("d-none");

  document.querySelectorAll('button[data-dismiss="modal"]').forEach(button => {
    button.addEventListener('click', function () {
      $('#addAiChatQuestionModal').modal('hide');
    });
  });

  function _submit() {
    // Save here
    setTimeout(function () {
      $("#submit_lesson_btn").click();
    }, 1000);
  }

  function onSumbmitClick() {
    $(".save-status").css("color", "darkgray").text("保存中");
  }

  $(document).ready(function() {
    // Check exist simpleMDE on screen
    if ($(".CodeMirror").length === 0) {
      addSimpleMdebyClass(".<%= simple_mde_class %>", function () {
        $("#submit_lesson_btn").click();
      }, function() {
        isEditorChanged = true
      });
      addSimpleMdebyClass(".<%= simple_mde_class_description %>", function () {
        $("#submit_lesson_btn").click();
      }, function() {
        isEditorChanged = true
      });
    };
    if ($("#lesson_subtitle_h2").is(":checked")) {
      $(".subtitle_h3").show()
    } else {
      $(".subtitle_h3").hide()
    }
    $("#lesson_subtitle_h2").change(function () {
      if (this.checked) {
        $(".subtitle_h3").show()
      } else {
        $("#lesson_subtitle_h3").prop('checked', false);
        $(".subtitle_h3").hide()
      }
    });

    $(".video_confim").click(function(){
      let input_val = $("#lesson_video").val()

      const iframeUrl = function (){
        if (!input_val) return null

        let type = 'youtube'
        let id = input_val
        if (id.includes("vimeo") || /^\d+$/.test(id)) {
          type = 'vimeo'
        }


        if (id.includes("/")) {
          if (type === "vimeo") {
            const matching = id.match(/(http|https)?:\/\/(www\.|player\.)?vimeo.com\/(?:channels\/(?:\w+\/)?|groups\/([^\/]*)\/videos\/|manage\/(?:\w+\/)|video\/|)(\d+)(?:|\/\?)/)
            id = matching && matching[4] ? matching[4] : null
          } else {
            const matching = id.split(/(vi\/|v=|\/v\/|youtu\.be\/|\/embed\/)/)
            id = matching[2] !== undefined ? matching[2].split(/[^0-9a-z_\-]/i)[0] : matching[0]
          }
        }

        if (!id) return null

        if (type === 'youtube') {
          return `https://www.youtube.com/embed/${id}`
        } else {
          return `https://player.vimeo.com/video/${id}`
        }
      }()

      if (iframeUrl) {
        $(".youtube-video").show().attr("src", iframeUrl)
        $(".no_image_video").hide()
        $("#lesson_video").val(iframeUrl.match(/\/(\w+)$/)[1])
      } else {
        $(".youtube-video").hide()
        $(".no_image_video").show()
      }
    })

    $(".video_confim").click()

    $(".vimeo-tab").click(function () {
      $(".admin-vimeo-video").show();
      $(".admin-youtube-video").hide()
      $(".youtube-tab-active-bar").hide()
      $(".vimeo-tab-active-bar").show()
      $("#lesson_video_type").val('2')
    });
    $(".youtube-tab").click(function () {
      $(".admin-vimeo-video").hide();
      $(".admin-youtube-video").show()
      $(".youtube-tab-active-bar").show()
      $(".vimeo-tab-active-bar").hide()
      $("#lesson_video_type").val('1')
    });

    $(".lesson_name_input").on('input', function(e) {
      $("#lesson_name_container").text(e.currentTarget.value)
    });

    $(document).on("click", "#lesson_image", function () {
      $("#lesson_image").on("change", function (e) {
        var reader = new FileReader();
        reader.readAsDataURL(e.target.files[0]);
        reader.onload = function (e) {
          $(".show-image").show()
          $(".view-image").show()
          $(".show-image").attr("style", "width: 60px; height: 40px;")
          $(".show-image").attr("src", e.target.result);
          $(".delete-image").show()
          $("#submit_lesson_btn").click()
        }
      })
    })
    $(".delete-image").click(function () {
      var check = confirm("本当に削除してよろしいですか？");
      if (check == true) {
        $("#lesson_image").val("")
        $(".show-image").hide()
        $(".view-image").hide()
        $(".delete-image").hide()
        $("#lesson_image_cache").val("delete")
        $("#submit_lesson_btn").click()
      } else {
        return false
      }
    })

    $("#lesson_type_video, #lesson_type_live").click(function () {
      $("#lesson_type_video_block").show()
      $("#lesson_type_text_block").hide()
      $("#lesson_type_exam_block").hide()
      $("#previewable-setting").show()
    })
    $("#lesson_type_text").click(function () {
      $("#lesson_type_video_block").hide()
      $("#lesson_type_text_block").show()
      $("#lesson_type_exam_block").hide()
      $("#previewable-setting").show()
    })
    $("#lesson_type_exam").click(function () {
      $("#lesson_type_video_block").hide()
      $("#lesson_type_text_block").hide()
      $("#lesson_type_exam_block").show()

      // previewableをfalseにする
      $("#lesson_is_previewable").prop('checked', false);
      $("#previewable-setting").hide()
    })
    $(".refresh-btn").click(function(){
      $(".vimeo-processing").hide()
      $(".vimeo-url").hide()
      var url = $(".refresh-btn").attr("data-url")
      $(".vimeo-url").attr("src", "")
      $(".vimeo-url").attr("src", url)
      $(".vimeo-url").show()
      $(".upload-videmo").show()
      $(".remove-video").show()
    })

    // ビデオが動いています。 
    if ($("#lesson_video_type").val() == '2') {
      setTimeout(() => {
        $(".vimeo-tab").click()
        $(".vimeo-tab").addClass("mdc-tab--active")
        $(".vimeo-tab-indicator").addClass("mdc-tab-indicator--active")
        $(".youtube-tab").removeClass("mdc-tab--active")
        $(".youtube-tab-indicator").removeClass("mdc-tab-indicator--active")

        $(".youtube-tab-active-bar").hide()
        $(".vimeo-tab-active-bar").show()
      }, 100);
    } else {
        $(".youtube-tab-active-bar").show()
        $(".vimeo-tab-active-bar").hide()
    }

    ClassicEditor
      .create(document.querySelector('#lesson-setting-ck'), {
        licenseKey: '',
        simpleUpload: {
          // Upload the images to the server using the CKFinder QuickUpload command.
          uploadUrl: '/pictures'
        }
      })
      .then(editor => {
        window.editor = editor;
        editor.model.document.on( 'change:data', () => {
          isEditorChanged = true
        })
      })
      .catch(error => {
        console.error('Oops, something went wrong!');
        console.error(
          'Please, report the following error on https://github.com/ckeditor/ckeditor5/issues with the build id and the error stack trace:'
          );
        console.warn('Build id: lxq9hpkltlpe-1aze6kf9qb0d');
        console.error(error);
      });
      
    $(".body_type_md").click(function(e) {
      update_type_editor()
    });
    $(".body_type_ck").click(function(e) {
      update_type_editor()
    });

    function update_type_editor() {
      if($(".body_type_md").is(':checked')){
        $(".md_body_editor").show()
        $(".ck_body_editor").hide()
      } else {
        $(".md_body_editor").hide()
        $(".ck_body_editor").show()
      }
    }
    update_type_editor()

    window.addEventListener('beforeunload', function (e) {
        if (window.vimeo_changing) {
          e.preventDefault(); 
          e.returnValue = ''; 
          alert("アップロード中ファイルがあります。よろしでしょうか？");
        }
    });

    let title = ""
    let mdBody = ""
    let ckBody = ""

    const loadCurrent = () => {
      title = $("#lesson_name").val()
      mdBody = $("#blog_body").val()
      ckBody = $("#lesson-setting-ck").val()
    }

    loadCurrent()

    // Vector generation button handling (Python API)
    $("#generate-vector-btn").on("click", function() {
      if (isEditorChanged) {
        alert("変更（レッスン名と本文）は、ベクター生成前に保存し、更新する必要があります。")
        return
      }

      const lessonId = $(this).data("lesson-id");
      const progressElement = $("#vector-generation-progress");
      const statusMessage = $("#vector-status-message");

      if (!lessonId) {
        alert("レッスンを保存してから実行してください。");
        return;
      }

      $("#vector-generated-timestamp").addClass("d-none");

      $(this).prop("disabled", true);
      $("#generate-vector-rails-btn").prop("disabled", true); // Disable Rails button too
      progressElement.removeClass("d-none");

      // Start the vector generation
      $.ajax({
        url: `/admin/schools/<%= @school.id %>/courses/<%= @course.id %>/lessons/${lessonId}/generate_vector`,
        method: "POST",
        dataType: "json",
        success: function(response) {
          console.log("Vector generation started:", response);

          // Setup the websocket connection to monitor progress
          setupVectorGenerationChannel(lessonId);
        },
        error: function(error) {
          console.error("Failed to start vector generation:", error);
          statusMessage.text("ベクター生成の開始に失敗しました");
          $("#vector-generated-timestamp").removeClass("d-none");
          setTimeout(function() {
            $("#generate-vector-btn").prop("disabled", false);
            $("#generate-vector-rails-btn").prop("disabled", false);
            progressElement.addClass("d-none");
          }, 3000);
        }
      });
    });

    // Vector generation button handling (Rails native)
    $("#generate-vector-rails-btn").on("click", function() {
      if (isEditorChanged) {
        alert("変更（レッスン名と本文）は、ベクター生成前に保存し、更新する必要があります。")
        return
      }

      const lessonId = $(this).data("lesson-id");
      const progressElement = $("#vector-generation-progress");
      const statusMessage = $("#vector-status-message");

      if (!lessonId) {
        alert("レッスンを保存してから実行してください。");
        return;
      }

      $("#vector-generated-timestamp").addClass("d-none");

      $(this).prop("disabled", true);
      $("#generate-vector-btn").prop("disabled", true); // Disable Python button too
      progressElement.removeClass("d-none");

      // Start the Rails-native vector generation
      $.ajax({
        url: `/admin/schools/<%= @school.id %>/courses/<%= @course.id %>/lessons/${lessonId}/generate_vector_rails`,
        method: "POST",
        dataType: "json",
        success: function(response) {
          console.log("Rails vector generation started:", response);

          // Setup the websocket connection to monitor progress
          setupVectorGenerationChannel(lessonId);
        },
        error: function(error) {
          console.error("Failed to start Rails vector generation:", error);
          statusMessage.text("Rails ベクター生成の開始に失敗しました");
          $("#vector-generated-timestamp").removeClass("d-none");
          setTimeout(function() {
            $("#generate-vector-btn").prop("disabled", false);
            $("#generate-vector-rails-btn").prop("disabled", false);
            progressElement.addClass("d-none");
          }, 3000);
        }
      });
    });

    function setupVectorGenerationChannel(lessonId) {
      if (App.cable && App.cable.subscriptions) {
        const subscription = App.cable.subscriptions.create(
          { channel: "VectorGenerationChannel", lesson_id: lessonId },
          {
            connected: function() {
              console.log("Connected to VectorGenerationChannel");
            },
            disconnected: function() {
              console.log("Disconnected from VectorGenerationChannel");
            },
            received: function(data) {
              console.log("Received vector generation update:", data);

              // Update the status message
              $("#vector-status-message").text(data.message);

              // Update progress bar if progress is available
              if (data.progress !== undefined) {
                const progress = Math.min(Math.round(data.progress), 100);
                $("#vector-progress-bar").css("width", progress + "%").attr("aria-valuenow", progress);
                $("#vector-progress-percentage").text(progress + "%");

                // Change color based on progress
                if (progress < 30) {
                  $("#vector-progress-bar").removeClass("bg-success bg-warning").addClass("bg-info");
                } else if (progress < 70) {
                  $("#vector-progress-bar").removeClass("bg-success bg-info").addClass("bg-warning");
                } else if (progress >= 70) {
                  $("#vector-progress-bar").removeClass("bg-warning bg-info").addClass("bg-success");
                }
              }

              // Handle completion
              if (data.status === "completed") {
                $("#vector-progress-bar").css("width", "100%").attr("aria-valuenow", 100);
                $("#vector-progress-percentage").text("100%");
                $("#vector-progress-bar").removeClass("progress-bar-animated").addClass("bg-success");

                if (data.vector_generated_at) {
                  if ($("#vector-generated-timestamp").length) {
                    $("#vector-generated-timestamp").text("最終保存: " + data.vector_generated_at)
                  } else {
                    $("<small class='text-muted me-2' id='vector-generated-timestamp'>最終保存: " + 
                      data.vector_generated_at + "</small>").insertAfter("#generate-vector-btn");
                  }
                }

                setTimeout(function() {
                  $("#generate-vector-btn").prop("disabled", false);
                  $("#generate-vector-rails-btn").prop("disabled", false);
                  $("#vector-generation-progress").addClass("d-none");
                  $("#vector-generated-timestamp").removeClass("d-none");
                }, 3000);
              }
              // Handle error
              else if (data.status === "error") {
                $("#vector-progress-bar").removeClass("bg-info bg-warning bg-success").addClass("bg-danger");

                $("#vector-generated-timestamp").removeClass("d-none");

                setTimeout(function() {
                  $("#generate-vector-btn").prop("disabled", false);
                  $("#generate-vector-rails-btn").prop("disabled", false);
                  $("#vector-generation-progress").addClass("d-none");
                }, 5000);
              }
            }
          }
        );

        return subscription;
      } else {
        console.error("ActionCable not available");
        $("#vector-status-message").text("進捗状況の追跡に失敗しました");
        $("#vector-generated-timestamp").removeClass("d-none");
        return null;
      }
    }
  });
</script>

<style>
  .CodeMirror,
  .CodeMirror-scroll {
    min-height: 200px;
  }

  .upload-progress {
    float: none;
  }

  /* Add this to your existing style block */
  #vector-progress-percentage {
    min-width: 40px;
    font-weight: bold;
  }

  #generate-vector-btn .material-icons,
  #generate-vector-rails-btn .material-icons {
    font-size: 16px;
    vertical-align: text-bottom;
  }

  .progress-bar-animated {
    animation: progress-bar-stripes 1s linear infinite;
  }

  .progress {
    border-radius: 4px;
    background-color: rgba(0, 0, 0, 0.1);
    overflow: hidden;
  }

  #vector-progress-bar {
    transition: width 0.3s ease, background-color 0.5s ease;
  }

  .mdc-layout-grid__cell--span-4.text-end .d-flex {
    flex-wrap: nowrap;
  }

  /* Progress container styling */
  #vector-generation-progress {
    min-width: 200px;
    max-width: 200px;
  }

  #vector-status-message {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    font-size: 0.75rem;
    max-width: 170px;
  }

  #generate-vector-btn,
  #generate-vector-rails-btn {
    white-space: nowrap;
  }

  #vector-progress-bar {
    transition: width 0.3s ease, background-color 0.5s ease;
  }

  .d-flex.align-items-center.justify-content-end {
    overflow: hidden;
  }
</style>
