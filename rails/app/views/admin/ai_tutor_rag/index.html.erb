<% content_for :title, "AIエージェント管理システム - RAG設定 - #{@school.name}" %>

<%= render 'admin/shared/ai_tutor_styles' %>

<style>
	.section-card {
		background: var(--bg-secondary);
		border: 1px solid var(--border-color);
		border-radius: 12px;
		padding: 1.5rem;
		margin-bottom: 1.5rem;
		box-shadow: 0 2px 10px rgba(0,0,0,0.05);
	}

	.section-header {
		display: flex;
		align-items: center;
		justify-content: space-between;
		margin-bottom: 1rem;
	}

	.section-title {
		font-weight: 600;
		font-size: 1.1rem;
		display: flex;
		align-items: center;
	}

	.section-title i {
		margin-right: 0.5rem;
		color: var(--accent-color);
	}

	.log-container {
		background-color: #1e1e1e;
		color: #d4d4d4;
		border-radius: 6px;
		padding: 1rem;
		font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
		font-size: 0.75rem;
		max-height: 400px;
		overflow-y: auto;
		border: 1px solid var(--border-color);
	}

	.log-entry {
		margin-bottom: 0.5rem;
		display: flex;
		line-height: 1.4;
	}

	.log-timestamp {
		color: #9ca3af;
		margin-right: 0.5rem;
		min-width: 80px;
		font-size: 0.7rem;
	}
</style>

<div class="main-container">
	<%= render 'admin/shared/ai_tutor_navigation', current_page: 'rag' %>

	<!-- Content Area -->
	<div class="content-area">

		<% unless @config_exists %>
			<!-- Setup Section -->
			<div class="section-card">
				<div class="text-center py-5">
					<i class="bi bi-gear-fill text-primary" style="font-size: 4rem;"></i>
					<h3 class="mt-3 mb-3">RAG設定の初期化</h3>
					<p class="text-muted mb-4">
						このスクールでRAG機能を使用するには、まず設定を作成する必要があります。<br>
						デフォルト設定で初期化した後、詳細設定をカスタマイズできます。
					</p>
					<button class="btn btn-primary btn-lg" id="create-config-btn">
						<i class="bi bi-plus-circle me-2"></i>
						RAG設定を作成
					</button>
				</div>
			</div>
		<% else %>

			<div class="row">
				<!-- Configuration Section -->
				<div class="col-lg-6">
					<div class="section-card">
						<div class="section-header">
							<div class="section-title">
								<i class="bi bi-sliders"></i>RAG設定
							</div>
						</div>
						
						<form id="config-form">
							<div class="mb-3">
								<div class="form-check form-switch">
									<input class="form-check-input" type="checkbox" id="enabled" <%= 'checked' if @rag_config_data&.enabled %>>
									<label class="form-check-label" for="enabled">
										RAG機能を有効にする
									</label>
								</div>
							</div>

							<div class="mb-3">
								<label class="form-label">Pinecone API Key</label>
								<div class="input-group">
									<input type="password" class="form-control" id="pinecone-api-key"
										placeholder="<%= @rag_config_data&.pinecone_api_key.present? ? 'APIキーが設定済み' : 'pc-...' %>"
										value="<%= @rag_config_data&.pinecone_api_key %>">
									<button class="btn btn-outline-secondary toggle-password" type="button" data-target="pinecone-api-key">
										<i class="bi bi-eye"></i>
									</button>
								</div>
								<small class="text-muted">
									<a href="https://app.pinecone.io/" target="_blank">Pinecone Console</a>で取得したAPIキーを入力してください
								</small>
							</div>

							<div class="mb-3">
								<label class="form-label">Top K (取得件数)</label>
								<input type="number" class="form-control" id="top-k" value="<%= @rag_config_data&.default_top_k || 5 %>" min="1" max="<%= @rag_config_data&.max_results || 20 %>">
								<small class="text-muted">検索結果として取得する最大件数</small>
							</div>

							<div class="mb-3">
								<label class="form-label">類似度閾値</label>
								<input type="number" class="form-control" id="similarity-threshold" value="<%= @rag_config_data&.similarity_threshold || 0.7 %>" min="0" max="1" step="0.1">
								<small class="text-muted">検索結果の最小類似度スコア</small>
							</div>

							<div class="mb-3">
								<label class="form-label">Namespace</label>
								<select class="form-control" id="namespace">
									<% if @rag_config_data&.available_namespaces %>
										<% @rag_config_data&.available_namespaces&.each do |ns| %>
											<option value="<%= ns[:value] %>" <%= 'selected' if ns[:value] == @rag_config_data&.default_namespace %>><%= ns[:label] %></option>
										<% end %>
									<% else %>
										<option value="">デフォルト</option>
										<option value="school_<%= @school.id %>">school_<%= @school.id %></option>
									<% end %>
								</select>
								<small class="text-muted">検索対象を特定の分野に限定</small>
							</div>

							<div class="mb-3">
								<label class="form-label">カスタムNamespace</label>
								<textarea class="form-control" id="custom-namespaces" rows="3" placeholder="カンマ区切りで入力 (例: custom-math, custom-english)"><%= @rag_config_data&.custom_namespaces %></textarea>
								<small class="text-muted">独自のNamespaceを追加する場合</small>
							</div>

							<button type="submit" class="btn btn-success">
								<i class="bi bi-check-circle me-2"></i>設定保存
							</button>
						</form>
					</div>
				</div>

				<!-- Search Test Section -->
				<div class="col-lg-6">
					<div class="section-card">
						<div class="section-header">
							<div class="section-title">
								<i class="bi bi-search"></i>検索テスト
							</div>
						</div>
						
						<div class="mb-3">
							<label class="form-label">検索クエリ</label>
							<input type="text" class="form-control" id="search-query" 
								placeholder="例：過去形の作り方、数学の方程式、プログラミング基礎" 
								value="過去形の作り方">
							<small class="text-muted">自然言語で検索したい内容を入力</small>
						</div>
						
						<div class="mb-3">
							<label class="form-label">レッスンID（オプション）</label>
							<input type="text" class="form-control" id="lesson-id" 
								placeholder="特定のレッスンに絞り込む場合">
							<small class="text-muted">特定のレッスンの教材のみを検索対象にする場合</small>
						</div>
						
						<button class="btn btn-primary" id="search-btn">
							<i class="bi bi-search me-2"></i>検索実行
						</button>
						
						<div class="mt-4">
							<label class="form-label">検索結果</label>
							<div class="log-container" id="search-results">
								<div class="text-muted small">検索実行ボタンをクリックして結果を確認してください</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		<% end %>
	</div>

	<script>
		document.addEventListener('DOMContentLoaded', function() {
			const csrfToken = document.querySelector('meta[name="csrf-token"]').content;
			console.log('CSRF Token:', csrfToken);

			document.getElementById('search-btn')?.addEventListener('click', performSearch);
			document.getElementById('config-form')?.addEventListener('submit', saveConfig);

			const createConfigBtn = document.getElementById('create-config-btn');
			if (createConfigBtn) {
				console.log('Create config button found, adding event listener');
				createConfigBtn.addEventListener('click', createConfig);
			} else {
				console.log('Create config button not found');
			}

			// Allow Enter key to trigger search (only if search-query exists)
			const searchQuery = document.getElementById('search-query');
			if (searchQuery) {
				searchQuery.addEventListener('keypress', function(e) {
					if (e.key === 'Enter') {
						performSearch();
					}
				});
			}

			function performSearch() {
				const query = document.getElementById('search-query').value.trim();
				const lessonId = document.getElementById('lesson-id').value.trim();
				const topK = document.getElementById('top-k').value;
				const namespace = document.getElementById('namespace').value;
				const resultsContainer = document.getElementById('search-results');

				if (!query) {
					showToast('warning', '検索クエリを入力してください');
					return;
				}

				resultsContainer.innerHTML = '<div class="text-muted">検索中...</div>';

				const params = new URLSearchParams({
					query: query,
					top_k: topK
				});

				if (lessonId) params.append('lesson_id', lessonId);
				if (namespace) params.append('namespace', namespace);

				fetch('<%= search_admin_school_ai_tutor_rag_index_path(@school) %>?' + params, {
					method: 'POST',
					headers: {
						'X-CSRF-Token': csrfToken,
						'Content-Type': 'application/json'
					}
				})
				.then(response => response.json())
				.then(data => {
					displaySearchResults(data, resultsContainer);
				})
				.catch(error => {
					resultsContainer.innerHTML = `<div class="text-danger">エラー: ${error.message}</div>`;
					showToast('error', `検索エラー: ${error.message}`);
				});
			}

			function displaySearchResults(data, container) {
				const timestamp = new Date().toTimeString().slice(0, 8);
				let html = '';

				html += `<div class="log-entry">
					<span class="log-timestamp">${timestamp}</span>
					<span class="log-level-info">[INFO]</span>
					<span>検索実行: "${data.result?.query || ''}"</span>
				</div>`;

				if (data.success) {
					const results = data.result?.results || [];
					html += `<div class="log-entry">
						<span class="log-timestamp">${timestamp}</span>
						<span class="log-level-success">[SUCCESS]</span>
						<span>${results.length}件の関連教材を発見</span>
					</div>`;

					if (results.length > 0) {
						results.forEach((result, index) => {
							const text = result.text || 'N/A';
							const score = result.score || 0;
							html += `<div class="log-entry">
								<span class="log-timestamp">${timestamp}</span>
								<span class="log-level-info">[RESULT]</span>
								<span>${index + 1}. "${text.substring(0, 60)}..." (score: ${score.toFixed(3)})</span>
							</div>`;
						});
					} else {
						html += `<div class="log-entry">
							<span class="log-timestamp">${timestamp}</span>
							<span class="log-level-warning">[WARNING]</span>
							<span>検索結果が見つかりませんでした</span>
						</div>`;
					}
				} else {
					html += `<div class="log-entry">
						<span class="log-timestamp">${timestamp}</span>
						<span class="log-level-error">[ERROR]</span>
						<span>${data.message || 'Unknown error'}</span>
					</div>`;
				}

				container.innerHTML = html;
				container.scrollTop = container.scrollHeight;
			}



			function saveConfig(e) {
				e.preventDefault();

				const formData = {
					enabled: document.getElementById('enabled').checked,
					pinecone_api_key: document.getElementById('pinecone-api-key').value,
					default_top_k: parseInt(document.getElementById('top-k').value),
					similarity_threshold: parseFloat(document.getElementById('similarity-threshold').value),
					default_namespace: document.getElementById('namespace').value,
					custom_namespaces: document.getElementById('custom-namespaces').value
				};

				fetch('<%= update_config_admin_school_ai_tutor_rag_index_path(@school) %>', {
					method: 'PATCH',
					headers: {
						'X-CSRF-Token': csrfToken,
						'Content-Type': 'application/json'
					},
					body: JSON.stringify({ config: formData })
				})
				.then(response => response.json())
				.then(data => {
					if (data.success) {
						showToast('success', data.message);
					} else {
						showToast('error', data.message);
					}
				})
				.catch(error => {
					showToast('error', `設定保存エラー: ${error.message}`);
				});
			}

			function createConfig() {
				console.log('createConfig function called');

				fetch('<%= create_config_admin_school_ai_tutor_rag_index_path(@school) %>', {
					method: 'POST',
					headers: {
						'X-CSRF-Token': csrfToken,
						'Content-Type': 'application/json'
					}
				})
				.then(response => {
					console.log('Response received:', response.status);
					return response.json();
				})
				.then(data => {
					console.log('Response data:', data);
					if (data.success) {
						showToast('success', data.message);
						// Reload page to show config interface
						setTimeout(() => {
							window.location.reload();
						}, 1500);
					} else {
						showToast('error', data.message);
					}
				})
				.catch(error => {
					console.error('Error in createConfig:', error);
					showToast('error', `設定作成エラー: ${error.message}`);
				});
			}



			function showToast(type, message) {
				const toastContainer = document.createElement('div');
				toastContainer.style.cssText = `
					position: fixed;
					top: 20px;
					right: 20px;
					z-index: 9999;
					min-width: 300px;
					padding: 1rem 1.5rem;
					border-radius: 8px;
					color: white;
					font-weight: 500;
					box-shadow: 0 4px 12px rgba(0,0,0,0.15);
					display: flex;
					align-items: center;
					justify-content: space-between;
					animation: slideIn 0.3s ease-out;
				`;

				const bgColor = type === 'success' ? '#10b981' :
							type === 'warning' ? '#f59e0b' : '#ef4444';
				toastContainer.style.backgroundColor = bgColor;

				toastContainer.innerHTML = `
					<span>${message}</span>
					<button onclick="this.parentElement.remove()"
							style="background: none; border: none; color: white; margin-left: 1rem; font-size: 1.2rem;">
						<i class="bi bi-x"></i>
					</button>
				`;

				document.body.appendChild(toastContainer);

				setTimeout(() => {
					toastContainer.remove();
				}, 5000);
			}

			// Password toggle functionality
			document.querySelectorAll('.toggle-password').forEach(button => {
				button.addEventListener('click', function() {
					const target = this.getAttribute('data-target');
					const inputField = document.getElementById(target);
					const icon = this.querySelector('i');

					if (inputField.type === 'password') {
						inputField.type = 'text';
						icon.classList.remove('bi-eye');
						icon.classList.add('bi-eye-slash');
					} else {
						inputField.type = 'password';
						icon.classList.remove('bi-eye-slash');
						icon.classList.add('bi-eye');
					}
				});
			});
		});
	</script>

	<style>
		@keyframes slideIn {
			from {
				transform: translateX(100%);
				opacity: 0;
			}
			to {
				transform: translateX(0);
				opacity: 1;
			}
		}
	</style>
</div>
