class Admin::LessonsController < Admin::ApplicationController
  protect_from_forgery with: :null_session
  load_and_authorize_resource :school
  before_action :school_onboarding_detect
  load_resource :goal
  authorize_resource :goal, if: -> {@goal.present?}
  load_and_authorize_resource :course, through: :school
  load_and_authorize_resource through: :course, except: [:sort, :show_modal_ai_chat_question, :save_ai_chat_question, :remove_ai_chat_question, :delete_material]
  before_action :set_lessons, only: [:index, :show, :edit, :update_chapters, :link_to_exam, :check_video_avaible]
  layout 'admin_material'

  add_breadcrumb -> { "レッスン一覧" }, path: [:admin, :@school, :courses, :lessons]

  def index
    @course_lessons = @course.course_lessons.includes(lesson: [:enrollment_lessons, :skill_items]).rank(:row_order)
    if params["published"] == "true"
      @course_lessons = @course_lessons.published
    end
    if params["published"] == "false"
      @course_lessons = @course_lessons.not_published
    end
    if params["name"].present?
      @course_lessons = @course_lessons.joins(:lesson).where("lessons.name LIKE ?", "%#{params["name"]}%")
    end

    @chapters = @course.chapters
  end

  def show
    @questions = @lesson.questions
  end

  def new
    lesson = @course.lessons.create({name: "", lesson_type: :video})
    redirect_to url_for([:edit, :admin, @school, @goal, @course, lesson]), notice: t('message.create')
  end

  def edit
    @skill_items = @goal.present? ? SkillItem.where(skill: @goal.skills) : []
    @skill = @goal.present? ? @goal.skills.first : []
    @materials = @lesson.materials.order(created_at: :desc)
    @exam_search = params[:exam_search]
    @exams = @school.exams
      .published
      .where(target_user_id: nil)
      .where("name like ?", "%#{@exam_search}%")
      .order(created_at: :desc)
      .page(params[:page])
      .per(10)
    @exam = Exam.where(id: @lesson.exam_id).first
  end

  def update
    if params[:lesson][:image_cache] == "delete"
      @lesson.remove_image!
    end

    respond_to do |format|
      update_params = lesson_params.merge({
        schedule_published_at: schedule_published_at
      })

      materials_params = update_params.delete(:materials)&.reject(&:blank?) || []
      if @lesson.update(update_params) && @lesson.materials.attach(materials_params)
        @materials = @lesson.materials.order(created_at: :desc)
        @lesson.update_teachers(params[:lesson][:teacher_ids])
        format.json { render :update, status: :ok, location: @lesson }
        format.html { redirect_to action: :edit }
        format.js
      else
        format.json { render json: @lesson.errors, status: :unprocessable_entity }
        format.js { render :update_error, status: :unprocessable_entity }
        format.html { redirect_to action: :index, notice: "エラー発生しました。" }
      end
    end
  end

  def destroy
    @lesson.destroy
    VimeoLog.remove_not_use_video(@school)
    respond_to do |format|
      format.html { redirect_to action: :index, notice: t('message.destroy') }
      format.json { head :no_content }
    end
  end

  def update_chapter
    respond_to do |format|
      if @lesson.update(chapter_id: params[:chapter_id])
        format.json { render json: "success", status: :ok }
      else
        format.json { render json: "failed", status: :unprocessable_entity }
      end
    end
  end

  def show_modal_ai_chat_question
    @lesson = @course.lessons.find(params[:id])
    @ai_chat_question = @lesson.ai_chat_questions.find_or_initialize_by(id: params[:ai_chat_question])
    respond_to do |format|
      format.js
    end
  end

  def save_ai_chat_question
    @lesson = @course.lessons.find(params[:id])
    @action = "create"
    if ai_chat_question_params[:id].present?
      @ai_chat_question = @lesson.ai_chat_questions.find(ai_chat_question_params[:id])
      @ai_chat_question.update(ai_chat_question_params)
      @action = "update"
    else
      @ai_chat_question = @school.ai_chat_questions.build(ai_chat_question_params)
      @ai_chat_question.context = :lesson
      @ai_chat_question.default = false

      ActiveRecord::Base.transaction do
        if @ai_chat_question.save
          @lesson.ai_chat_questions << @ai_chat_question
        end
      end
    end

    respond_to do |format|
      format.js
    end
  end

  def remove_ai_chat_question
    @lesson = @course.lessons.find(params[:id])
    @ai_chat_question = @lesson.ai_chat_questions.find(params[:ai_chat_question])
    @ai_chat_question.destroy

    respond_to do |format|
      format.js
    end
  end

  def sort
    course_lesson = CourseLesson.find params[:id]
    course_lesson.update(course_lesson_params)
  end

  def vimeo
    body = {
      upload: {
        approach: "tus",
        size: params[:size]
      }
    }
    vimeo_util = VimeoUtil.new(@school)
    vimeo_info = vimeo_util.client.post('/me/videos?fields=uri,upload', body: body)
    vimeo_id =  vimeo_info["uri"].split("/").last

    # Delete video if not use
    RemoveUncompletedVideoWorker.perform_at(24.hours.from_now, vimeo_id)

    # get upload_folder_id
    upload_folder_id = vimeo_util.get_or_create_folder("#{ENV["VIMEO_PRE_UPLOAD_FOLDER_NAME"]}")

    # Move to pre upload folder
    vimeo_util.client.put("/me/projects/#{upload_folder_id}/videos/#{vimeo_id}", code: 204)

    # Rename video: Max 128
    name = ""
    if params[:lesson_id] && Lesson.where(id: params[:lesson_id]).first
      name = Lesson.where(id: params[:lesson_id]).first.name
    end
    name = "未定" unless name.present?

    vimeo_util.client.patch("/videos/#{vimeo_id}", body: {
      name: name[0, 128]
    })

    respond_to do |format|
      format.json {render json: vimeo_info, status: :ok}
    end
  end

  def classroom_preview
    raise CanCan::AccessDenied.new("Not authorized!") unless current_user.present?

    SchoolManagerUser.init current_user
    unless SchoolManagerUser.admin_or_exam_manager? || current_user.is_manager?(SchoolManager.main_school)
      raise CanCan::AccessDenied.new("Not authorized!")
    end
    lesson = Lesson.find(params[:id])
    enrollment = Enrollment.find_or_create_by(user_id: current_user.id, course_id: lesson.course.id)

    redirect_to "/classroom/my-courses/#{lesson.course.id}?lesson_id=#{lesson.id}"
  end

  def update_published
    published_at = @lesson.published? ? nil : Time.zone.now
    @lesson.update(published_at: published_at)
    respond_to do |format|
      format.html { redirect_back(fallback_location: root_path, notice: t('message.update')) }
      format.json { head :no_content }
      format.js
    end
  end

  def copy
    saved_lesson = @course.lessons.create({
      name: "コピー ~ #{@lesson.name}",
      description: @lesson.description,
      body: @lesson.body,
      md_body: @lesson.md_body,
      ck_body: @lesson.ck_body,
      body_type: @lesson.body_type,
      video: @lesson.video,
      duration: @lesson.duration,
      vimeo: @lesson.vimeo,
      vimeo_complete_uri: @lesson.vimeo_complete_uri,
      update_child_mimic: @lesson.update_child_mimic,
      image: @lesson.image,
      published_at: nil,
      subtitle_h2: @lesson.subtitle_h2,
      subtitle_h3: @lesson.subtitle_h3,
      lesson_type: @lesson.lesson_type,
      skill_item_ids: [],
      materials: []
    })
    flash[:notice] = "レッスンがコピーされました。"
    redirect_to url_for([:edit, :admin, @school, @goal, @course, saved_lesson])
  end

  def link_to_exam
    @lesson.update!(exam_id: params[:exam_id] || 0)
    @exam = Exam.where(id: @lesson.exam_id).first
    @exam_search = params[:exam_search]
    @exams = @school.exams
      .published
      .where(target_user_id: nil)
      .where("name like ?", "%#{@exam_search}%")
      .order(created_at: :desc)
      .page(params[:page])
      .per(10)
  end

  def multi_delete
    @course.lessons.where(id: params[:selected_ids]).destroy_all()
  end

  def multi_chapter_edit
    @course.lessons.where(id: params[:selected_ids]).update_all(chapter_id: params[:chapter_id])
  end

  def multi_publish
    @course.lessons.where(id: params[:selected_ids]).update_all(published_at: Time.zone.now)
  end

  def multi_unpublish
    @course.lessons.where(id: params[:selected_ids]).update_all(published_at: nil)
  end

  def check_vimeo
    vimeo_util = VimeoUtil.new(@school)
    @vimeo_video_info = vimeo_util.client.get("/videos/#{params[:vimeo_id]}")
    # js処理
  end

  def generate_vector
    @lesson = Lesson.find(params[:id])

    GenerateVectorJob.perform_later(@lesson.id)

    respond_to do |format|
      format.json { render json: { status: 'started', message: 'ベクター生成ジョブが開始されました' } }
    end
  end

  def generate_vector_rails
    @lesson = Lesson.find(params[:id])

    GenerateVectorRailsJob.perform_later(@lesson.id)

    respond_to do |format|
      format.json { render json: { status: 'started', message: 'Rails ベクター生成ジョブが開始されました' } }
    end
  end

  def delete_material
    @lesson = @course.lessons.find(params[:id])
    @material = @lesson.materials.find(params[:material_id])

    @material.purge_later

    respond_to do |format|
      format.html { redirect_back fallback_location: edit_admin_school_goal_course_lesson_path(@school, @goal, @course, @lesson), notice: '素材を削除しました' }
      format.json { render json: { status: :success }, status: :ok }
      format.js
    end
  end

  private

  def set_lesson
    @lesson = Lesson.find(params[:id])
  end

  def set_lessons
    @course_lessons = @course.course_lessons.includes(lesson: [:comments, :votes, :enrollment_lessons, :skill_items]).rank(:row_order)
  end

  def lesson_params
    params.require(:lesson).permit(
      :name,
      :description,
      :body,
      :md_body,
      :video,
      :video_type,
      :duration,
      :vimeo,
      :vimeo_complete_uri,
      :update_child_mimic,
      :published,
      :image,
      :published_at,
      :subtitle_h2,
      :subtitle_h3,
      :lesson_type,
      :is_previewable,
      :ck_body,
      :body_type,
      :skill_item_ids => [],
      :materials => []
    )
  end

  def ai_chat_question_params
    params.require(:ai_chat_question).permit(:id, :title, :prompt, :image, :image_cache, :remove_image, :published_at, :icon, :icon_cache, :remove_icon)
  end

  def course_lesson_params
    params.require(:course_lesson).permit(:row_order_position)
  end

  def schedule_published_at
    schedule_published = params.permit(:schedule_published_date, :schedule_published_time)
    return nil unless schedule_published[:schedule_published_date].present? && schedule_published[:schedule_published_time].present?

    Time.zone.parse("#{schedule_published[:schedule_published_date]} #{schedule_published[:schedule_published_time]}")
  end
end
