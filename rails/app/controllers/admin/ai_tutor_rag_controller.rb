class Admin::AiT<PERSON>rRagController < Admin::ApplicationController
  before_action :set_school
  before_action :set_rag_config

  def index
    # Main RAG management page
    @rag_config_data = @rag_config
    @config_exists = @school.ai_tutor_rag_config.present?
  end

  def search
    unless @rag_config.present?
      render json: {
        success: false,
        error: 'RAG設定が存在しません。まず設定を作成してください。'
      }
      return
    end

    query = params[:query]
    lesson_id = params[:lesson_id]
    custom_params = {
      top_k: params[:top_k]&.to_i,
      namespace: params[:namespace],
      lesson_id: lesson_id
    }

    if query.blank?
      render json: {
        success: false,
        error: '検索クエリを入力してください'
      }
      return
    end

    # Use config-based search parameters
    search_params = @rag_config.search_params_for(custom_params)

    result = AiTutorRagService.search_materials(
      query: query,
      school: @school,
      **search_params
    )

    # Update usage stats
    if result[:success]
      @rag_config.increment_successful_search!
    else
      @rag_config.increment_failed_search!
    end

    render json: {
      success: result[:success],
      message: result[:success] ? '検索が完了しました' : result[:error],
      result: result,
      config_used: search_params
    }
  end

  def show_config
    unless @rag_config.present?
      render json: {
        success: false,
        message: 'RAG設定が存在しません。まず設定を作成してください。'
      }
      return
    end

    render json: {
      success: true,
      config: @rag_config.to_search_config,
      stats: {
        total_searches: @rag_config.total_searches,
        successful_searches: @rag_config.successful_searches,
        failed_searches: @rag_config.failed_searches,
        success_rate: @rag_config.success_rate,
        last_used_at: @rag_config.last_used_at
      }
    }
  end

  def create_config
    if @school.ai_tutor_rag_config.present?
      render json: {
        success: false,
        message: 'RAG設定は既に存在します'
      }
      return
    end

    @rag_config = @school.build_ai_tutor_rag_config(
      enabled: true,
      default_top_k: 5,
      default_namespace: 'development',
      similarity_threshold: 0.7,
      include_metadata: true,
      max_results: 20
    )

    if @rag_config.save
      render json: {
        success: true,
        message: 'RAG設定が作成されました',
        config: @rag_config.to_search_config
      }
    else
      render json: {
        success: false,
        message: @rag_config.errors.full_messages.join(', '),
        errors: @rag_config.errors
      }
    end
  end

  def update_config
    unless @school.ai_tutor_rag_config.present?
      render json: {
        success: false,
        message: 'RAG設定が存在しません。まず設定を作成してください。'
      }
      return
    end

    config_params = params.require(:config).permit(
      :enabled, :default_top_k, :default_namespace, :similarity_threshold,
      :include_metadata, :max_results, :custom_namespaces, :pinecone_api_key
    )

    if @rag_config.update(config_params)
      render json: {
        success: true,
        message: 'RAG設定が更新されました',
        config: @rag_config.to_search_config
      }
    else
      render json: {
        success: false,
        message: @rag_config.errors.full_messages.join(', '),
        errors: @rag_config.errors
      }
    end
  end

  private

  def set_school
    @school = School.find(params[:school_id])
  end

  def set_rag_config
    @rag_config = @school.ai_tutor_rag_config
  end
end
