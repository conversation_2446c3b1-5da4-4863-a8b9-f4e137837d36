# Pinecone Configuration for RAG Search

Rails.application.configure do
  # Pinecone configuration for RAG search only
  config.pinecone = ActiveSupport::OrderedOptions.new

  # Basic configuration from environment variables
  config.pinecone.api_key = ENV['PINECONE_API_KEY']
  config.pinecone.environment = ENV['PINECONE_ENV'] || 'us-east-1'
  config.pinecone.index_name = ENV['INDEX_NAME'] || 'edbase-dev'
end
